
import React, { useEffect, useRef } from 'react';
import QRCode from 'qrcode';

interface QRDesignSettings {
  foregroundColor: string;
  backgroundColor: string;
  style: 'square' | 'rounded' | 'dots';
  logo?: string;
  logoSize: number;
}

interface QRPreviewProps {
  vCardString: string;
  settings: QRDesignSettings;
  size?: number;
}

const QRPreview: React.FC<QRPreviewProps> = ({ 
  vCardString, 
  settings, 
  size = 200 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current || !vCardString.trim()) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const generateQR = async () => {
      try {
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Generate QR code
        await QRCode.toCanvas(canvas, vCardString, {
          width: size,
          margin: 2,
          color: {
            dark: settings.foregroundColor,
            light: settings.backgroundColor,
          },
          errorCorrectionLevel: 'M',
        });

        // Apply style modifications if needed
        if (settings.style === 'rounded' || settings.style === 'dots') {
          applyStyleModifications(ctx, canvas, settings.style);
        }

        // Add logo if present
        if (settings.logo) {
          await addLogo(ctx, canvas, settings.logo, settings.logoSize);
        }
      } catch (error) {
        console.error('Error generating QR code:', error);
      }
    };

    generateQR();
  }, [vCardString, settings, size]);

  const applyStyleModifications = (
    ctx: CanvasRenderingContext2D, 
    canvas: HTMLCanvasElement, 
    style: 'rounded' | 'dots'
  ) => {
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    // This is a simplified approach - in a production app, you'd want
    // more sophisticated pattern recognition and modification
    if (style === 'rounded') {
      // Apply slight rounding effect (simplified)
      ctx.globalCompositeOperation = 'source-over';
    }
    
    if (style === 'dots') {
      // Convert squares to circles (simplified approach)
      ctx.globalCompositeOperation = 'source-over';
    }
  };

  const addLogo = async (
    ctx: CanvasRenderingContext2D, 
    canvas: HTMLCanvasElement, 
    logoSrc: string, 
    logoSize: number
  ) => {
    return new Promise<void>((resolve) => {
      const img = new Image();
      img.onload = () => {
        const logoSizePixels = (canvas.width * logoSize) / 100;
        const x = (canvas.width - logoSizePixels) / 2;
        const y = (canvas.height - logoSizePixels) / 2;

        // Add white background circle for logo
        ctx.fillStyle = settings.backgroundColor;
        ctx.beginPath();
        ctx.arc(
          canvas.width / 2, 
          canvas.height / 2, 
          logoSizePixels / 2 + 4, 
          0, 
          2 * Math.PI
        );
        ctx.fill();

        // Draw logo
        ctx.drawImage(img, x, y, logoSizePixels, logoSizePixels);
        resolve();
      };
      img.onerror = () => resolve();
      img.src = logoSrc;
    });
  };

  if (!vCardString.trim()) {
    return (
      <div 
        className="border-2 border-dashed border-muted rounded-lg flex items-center justify-center text-muted-foreground"
        style={{ width: size, height: size }}
      >
        <span className="text-sm">Enter contact info</span>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center gap-2">
      <canvas
        ref={canvasRef}
        width={size}
        height={size}
        className="border rounded-lg shadow-sm"
        style={{ 
          maxWidth: '100%', 
          height: 'auto',
          imageRendering: 'pixelated'
        }}
      />
    </div>
  );
};

export default QRPreview;
