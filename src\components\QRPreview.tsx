
import React, { useEffect, useRef } from 'react';
import QRCode from 'qrcode';

interface QRDesignSettings {
  foregroundColor: string;
  backgroundColor: string;
  style: 'square' | 'rounded' | 'dots';
  logo?: string;
  logoSize: number;
  pattern?: string;
  eyeStyle?: string;
  frameStyle?: string;
  frameText?: string;
  frameFont?: string;
  template?: string;
  gradientType?: 'solid' | 'gradient';
}

interface QRPreviewProps {
  vCardString: string;
  settings: QRDesignSettings;
  size?: number;
}

// Helper function for rounded rectangles
const drawRoundedRect = (
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  width: number,
  height: number,
  radius: number
) => {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
};

const QRPreview: React.FC<QRPreviewProps> = ({
  vCardString,
  settings,
  size = 200
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current || !vCardString.trim()) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const generateQR = async () => {
      try {
        console.log('Generating QR with settings:', settings);
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Generate QR code with custom rendering
        await QRCode.toCanvas(canvas, vCardString, {
          width: size,
          margin: 2,
          color: {
            dark: settings.foregroundColor,
            light: settings.backgroundColor,
          },
          errorCorrectionLevel: 'M',
        });

        // Apply pattern and eye style modifications
        if (settings.pattern || settings.style || settings.eyeStyle) {
          setTimeout(() => applyCustomStyles(ctx, canvas, settings), 10);
        }

        // Add logo if present
        if (settings.logo) {
          await addLogo(ctx, canvas, settings.logo, settings.logoSize);
        }
      } catch (error) {
        console.error('Error generating QR code:', error);
      }
    };

    generateQR();
  }, [vCardString, settings, size]);

  const applyCustomStyles = (
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement,
    settings: QRDesignSettings
  ) => {
    console.log('Applying custom styles:', settings.pattern, settings.eyeStyle);

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const width = canvas.width;
    const height = canvas.height;

    // Simple approach: modify existing pixels based on pattern
    if (settings.style === 'dots' || settings.pattern === 'dots') {
      // Convert squares to circles
      for (let y = 0; y < height; y += 4) {
        for (let x = 0; x < width; x += 4) {
          const pixelIndex = (y * width + x) * 4;
          if (data[pixelIndex] < 128) { // If it's a dark pixel
            // Clear the square area
            ctx.fillStyle = settings.backgroundColor;
            ctx.fillRect(x - 2, y - 2, 4, 4);

            // Draw a circle instead
            ctx.fillStyle = settings.foregroundColor;
            ctx.beginPath();
            ctx.arc(x, y, 1.5, 0, 2 * Math.PI);
            ctx.fill();
          }
        }
      }
    }

    // Apply eye style modifications to corners
    if (settings.eyeStyle && settings.eyeStyle !== 'square') {
      const eyeSize = Math.floor(width / 4);

      // Top-left eye
      applyEyeStyle(ctx, 10, 10, eyeSize, settings.eyeStyle, settings);
      // Top-right eye
      applyEyeStyle(ctx, width - eyeSize - 10, 10, eyeSize, settings.eyeStyle, settings);
      // Bottom-left eye
      applyEyeStyle(ctx, 10, height - eyeSize - 10, eyeSize, settings.eyeStyle, settings);
    }
  };

  const applyEyeStyle = (
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    size: number,
    eyeStyle: string,
    settings: QRDesignSettings
  ) => {
    ctx.save();

    if (eyeStyle === 'circle') {
      // Clear the square area
      ctx.fillStyle = settings.backgroundColor;
      ctx.fillRect(x, y, size, size);

      // Draw circular eye
      ctx.fillStyle = settings.foregroundColor;

      // Outer circle
      ctx.beginPath();
      ctx.arc(x + size/2, y + size/2, size/2 - 2, 0, 2 * Math.PI);
      ctx.fill();

      // Inner circle (background)
      ctx.fillStyle = settings.backgroundColor;
      ctx.beginPath();
      ctx.arc(x + size/2, y + size/2, size/2 - 8, 0, 2 * Math.PI);
      ctx.fill();

      // Center circle
      ctx.fillStyle = settings.foregroundColor;
      ctx.beginPath();
      ctx.arc(x + size/2, y + size/2, size/4, 0, 2 * Math.PI);
      ctx.fill();
    } else if (eyeStyle === 'rounded') {
      // Clear the square area
      ctx.fillStyle = settings.backgroundColor;
      ctx.fillRect(x, y, size, size);

      // Draw rounded eye
      ctx.fillStyle = settings.foregroundColor;

      // Outer rounded square
      drawRoundedRect(ctx, x + 2, y + 2, size - 4, size - 4, 4);
      ctx.fill();

      // Inner rounded square (background)
      ctx.fillStyle = settings.backgroundColor;
      drawRoundedRect(ctx, x + 8, y + 8, size - 16, size - 16, 2);
      ctx.fill();

      // Center rounded square
      ctx.fillStyle = settings.foregroundColor;
      drawRoundedRect(ctx, x + size/4, y + size/4, size/2, size/2, 2);
      ctx.fill();
    }

    ctx.restore();
  };



  const addLogo = async (
    ctx: CanvasRenderingContext2D, 
    canvas: HTMLCanvasElement, 
    logoSrc: string, 
    logoSize: number
  ) => {
    return new Promise<void>((resolve) => {
      const img = new Image();
      img.onload = () => {
        const logoSizePixels = (canvas.width * logoSize) / 100;
        const x = (canvas.width - logoSizePixels) / 2;
        const y = (canvas.height - logoSizePixels) / 2;

        // Add white background circle for logo
        ctx.fillStyle = settings.backgroundColor;
        ctx.beginPath();
        ctx.arc(
          canvas.width / 2, 
          canvas.height / 2, 
          logoSizePixels / 2 + 4, 
          0, 
          2 * Math.PI
        );
        ctx.fill();

        // Draw logo
        ctx.drawImage(img, x, y, logoSizePixels, logoSizePixels);
        resolve();
      };
      img.onerror = () => resolve();
      img.src = logoSrc;
    });
  };

  if (!vCardString.trim()) {
    return (
      <div 
        className="border-2 border-dashed border-muted rounded-lg flex items-center justify-center text-muted-foreground"
        style={{ width: size, height: size }}
      >
        <span className="text-sm">Enter contact info</span>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center gap-2">
      <canvas
        ref={canvasRef}
        width={size}
        height={size}
        className="border rounded-lg shadow-sm"
        style={{ 
          maxWidth: '100%', 
          height: 'auto',
          imageRendering: 'pixelated'
        }}
      />
    </div>
  );
};

export default QRPreview;
