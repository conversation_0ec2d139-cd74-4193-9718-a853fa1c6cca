# Network Access Configuration Documentation
## COP VCARD QR Generator by <PERSON>

### Document Information
- **Application**: COP VCARD QR Generator by <PERSON>
- **Version**: 1.0
- **Date**: December 12, 2025
- **Author**: <PERSON>
- **Purpose**: Enable organizational network access to localhost development server

---

## Executive Summary

This document outlines the configuration process for enabling network access to the COP VCARD QR Generator application, allowing multiple departments within the organization to access the application from their local machines while the development server runs on the host machine.

## Application Overview

The COP VCARD QR Generator is a React-based web application built with Vite that allows users to:
- Import contact data via CSV files
- Generate professional QR codes for vCard contact information
- Customize QR code design and appearance
- Bulk download high-resolution QR codes

## Network Configuration Process

### 1. Initial Setup Analysis

**Development Environment:**
- **Framework**: React with TypeScript
- **Build Tool**: Vite 5.4.10
- **Package Manager**: npm (Node.js)
- **Operating System**: Windows
- **Host Machine IP**: ************ (Wi-Fi network)

**Network Infrastructure Discovered:**
```
Ethernet adapter Ethernet 3: ************
Ethernet adapter Ethernet 4: ************
Wireless LAN adapter Wi-Fi: ************ (Primary)
```

### 2. Vite Server Configuration

**File**: `vite.config.ts`

**Original Configuration:**
```typescript
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  // ... other configurations
}));
```

**Key Configuration Elements:**
- `host: "::"` - Enables listening on all available network interfaces (IPv4 and IPv6)
- `port: 8080` - Initial port configuration (later changed to 3000)

### 3. Network Access Implementation

#### Step 1: Dependency Installation
```bash
# Add Node.js to PATH and install dependencies
$env:PATH += ";C:\Program Files\nodejs"
npm install
```

#### Step 2: Development Server Launch
```bash
# Start development server with network access
npm run dev
```

#### Step 3: Network Interface Detection
The Vite development server automatically detected and bound to multiple network interfaces:
```
➜  Local:   http://localhost:8080/
➜  Network: http://************:8080/
➜  Network: http://************:8080/
➜  Network: http://************:8080/
```

### 4. Port Configuration Optimization

**Rationale for Port Change:**
- Corporate firewalls often block port 8080
- Port 3000 is more commonly allowed in organizational networks
- Provides better compatibility across different network environments

**Updated Configuration:**
```typescript
server: {
  host: "::",
  port: 3000,
},
```

## Network Access URLs

### Primary Access Points

| Network Type | IP Address | URL | Purpose |
|--------------|------------|-----|---------|
| **Primary Wi-Fi** | ************ | `http://************:3000/` | **Recommended for organizational access** |
| Virtual Network 1 | ************ | `http://************:3000/` | Alternative access point |
| Virtual Network 2 | ************ | `http://************:3000/` | Alternative access point |
| Localhost | 127.0.0.1 | `http://localhost:3000/` | Host machine only |

### Recommended Access URL for Departments
**Primary URL**: `http://************:3000/`

## Security and Firewall Considerations

### Windows Firewall Configuration

**Automatic Rule Creation (Requires Administrator):**
```cmd
netsh advfirewall firewall add rule name="Vite Dev Server" dir=in action=allow protocol=TCP localport=3000
```

**Manual Configuration Steps:**
1. Open Windows Defender Firewall with Advanced Security (as Administrator)
2. Navigate to "Inbound Rules" → "New Rule"
3. Select "Port" → "TCP" → "Specific Local Ports"
4. Enter port number: `3000`
5. Select "Allow the connection"
6. Apply to all network profiles (Domain, Private, Public)
7. Name the rule: "COP VCARD QR Generator - Port 3000"

### Corporate Network Considerations

**Network Requirements:**
- Devices must be on the same subnet (10.18.x.x)
- Corporate firewall must allow inbound connections on port 3000
- Host machine must remain powered on and connected

**Security Best Practices:**
- Development server should only run during business hours
- Consider implementing basic authentication for production use
- Monitor access logs for unauthorized usage
- Regularly update dependencies for security patches

## Troubleshooting Guide

### Common Issues and Solutions

| Issue | Symptoms | Solution |
|-------|----------|----------|
| **Connection Refused** | Browser shows "This site can't be reached" | Check if development server is running |
| **Firewall Blocking** | Timeout errors from other machines | Configure Windows Firewall rules |
| **Wrong Network** | Can't access from other departments | Verify all devices are on 10.18.x.x subnet |
| **Port Conflicts** | Server fails to start | Change port in vite.config.ts |

### Diagnostic Commands

**Check Network Configuration:**
```cmd
ipconfig
```

**Test Port Availability:**
```cmd
netstat -an | findstr :3000
```

**Verify Firewall Rules:**
```cmd
netsh advfirewall firewall show rule name="Vite Dev Server"
```

## Deployment Recommendations

### For Permanent Organizational Access

**Option 1: Internal Web Server**
- Deploy to IIS or Apache on dedicated server
- Configure proper domain name (e.g., `qr-generator.cop.local`)
- Implement SSL/TLS certificates

**Option 2: Cloud Deployment**
- Deploy to Azure, AWS, or Google Cloud
- Configure organizational SSO integration
- Implement proper backup and monitoring

**Option 3: Docker Containerization**
- Create Docker container for easy deployment
- Use Docker Compose for production environment
- Implement container orchestration if needed

## Maintenance and Monitoring

### Regular Maintenance Tasks
- Monitor server performance and memory usage
- Update Node.js and npm dependencies monthly
- Review and rotate any authentication credentials
- Backup application data and configurations

### Performance Monitoring
- Monitor concurrent user connections
- Track application response times
- Monitor server resource utilization
- Log and analyze user access patterns

## Contact Information

**Technical Support:**
- **Developer**: David Jara
- **Application**: COP VCARD QR Generator
- **Documentation Version**: 1.0
- **Last Updated**: December 12, 2025

## Quick Start Guide for Department Users

### For End Users Accessing the Application

1. **Open your web browser** (Chrome, Firefox, Edge, Safari)
2. **Navigate to**: `http://************:3000/`
3. **Verify connection**: You should see the "COP VCARD QR Generator by David Jara" homepage
4. **If connection fails**: Contact IT support or the application administrator

### Application Usage Workflow
1. **Import Data**: Upload CSV file with contact information
2. **Review Data**: Verify imported contact details
3. **Customize Design**: Adjust QR code colors, style, and logo
4. **Generate & Download**: Create and download high-resolution QR codes

## Technical Specifications

### System Requirements

**Host Machine (Development Server):**
- Windows 10/11
- Node.js 18+ installed
- Minimum 4GB RAM
- Network connectivity
- Port 3000 available

**Client Machines (Department Users):**
- Any modern web browser
- Network access to 10.18.x.x subnet
- JavaScript enabled
- Minimum 1024x768 screen resolution

### Performance Characteristics
- **Concurrent Users**: Up to 10-15 simultaneous users recommended
- **File Upload Limit**: 10MB CSV files
- **QR Code Generation**: ~1-2 seconds per code
- **Bulk Processing**: Up to 100 contacts per batch

## Appendices

### Appendix A: Network Topology Diagram
```
Internet
    |
Corporate Router (*********)
    |
Corporate Network (*********/20)
    |
    ├── Host Machine (************:3000) [Development Server]
    ├── Department A (10.18.x.x) [Client Access]
    ├── Department B (10.18.x.x) [Client Access]
    └── Department C (10.18.x.x) [Client Access]
```

### Appendix B: Configuration Files

**package.json Scripts:**
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  }
}
```

**Complete vite.config.ts:**
```typescript
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 3000,
  },
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
```

### Appendix C: Change Log

| Date | Version | Changes | Author |
|------|---------|---------|--------|
| 2025-12-12 | 1.0 | Initial network configuration and documentation | David Jara |
| 2025-12-12 | 1.1 | Port change from 8080 to 3000 for better compatibility | David Jara |

---

*This documentation should be reviewed and updated whenever network configurations or application deployments change.*

**Document Classification**: Internal Use Only
**Distribution**: IT Department, Development Team, Department Managers
