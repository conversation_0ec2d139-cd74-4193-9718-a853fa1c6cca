
import React from 'react';
import { Trash2, Edit, UserCheck } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface VCardData {
  firstName: string;
  lastName: string;
  organization: string;
  jobTitle: string;
  address: string;
  city: string;
  phone: string;
  email: string;
}

interface DataReviewProps {
  data: VCardData[];
  onDataChange: (data: VCardData[]) => void;
  onProceedToCustomize: () => void;
}

const DataReview: React.FC<DataReviewProps> = ({ data, onDataChange, onProceedToCustomize }) => {
  const [editingIndex, setEditingIndex] = React.useState<number | null>(null);
  const [editData, setEditData] = React.useState<VCardData | null>(null);

  const handleEdit = (index: number) => {
    setEditingIndex(index);
    setEditData({ ...data[index] });
  };

  const handleSave = () => {
    if (editingIndex !== null && editData) {
      const newData = [...data];
      newData[editingIndex] = editData;
      onDataChange(newData);
      setEditingIndex(null);
      setEditData(null);
    }
  };

  const handleCancel = () => {
    setEditingIndex(null);
    setEditData(null);
  };

  const handleDelete = (index: number) => {
    const newData = data.filter((_, i) => i !== index);
    onDataChange(newData);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Review Contact Data ({data.length} contacts)
          </CardTitle>
        </CardHeader>
        <CardContent>
          {data.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No contact data to review. Please import data or add contacts manually.
            </div>
          ) : (
            <>
              <div className="rounded-md border overflow-auto max-h-96">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Organization</TableHead>
                      <TableHead>Job Title</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Phone</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.map((contact, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          {editingIndex === index ? (
                            <div className="space-y-2">
                              <Input
                                value={editData?.firstName || ''}
                                onChange={(e) => setEditData(prev => prev ? { ...prev, firstName: e.target.value } : null)}
                                placeholder="First Name"
                                className="text-xs"
                              />
                              <Input
                                value={editData?.lastName || ''}
                                onChange={(e) => setEditData(prev => prev ? { ...prev, lastName: e.target.value } : null)}
                                placeholder="Last Name"
                                className="text-xs"
                              />
                            </div>
                          ) : (
                            <div className="font-medium">
                              {contact.firstName} {contact.lastName}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          {editingIndex === index ? (
                            <Input
                              value={editData?.organization || ''}
                              onChange={(e) => setEditData(prev => prev ? { ...prev, organization: e.target.value } : null)}
                              placeholder="Organization"
                              className="text-xs"
                            />
                          ) : (
                            contact.organization
                          )}
                        </TableCell>
                        <TableCell>
                          {editingIndex === index ? (
                            <Input
                              value={editData?.jobTitle || ''}
                              onChange={(e) => setEditData(prev => prev ? { ...prev, jobTitle: e.target.value } : null)}
                              placeholder="Job Title"
                              className="text-xs"
                            />
                          ) : (
                            contact.jobTitle
                          )}
                        </TableCell>
                        <TableCell>
                          {editingIndex === index ? (
                            <Input
                              value={editData?.email || ''}
                              onChange={(e) => setEditData(prev => prev ? { ...prev, email: e.target.value } : null)}
                              placeholder="Email"
                              className="text-xs"
                            />
                          ) : (
                            contact.email
                          )}
                        </TableCell>
                        <TableCell>
                          {editingIndex === index ? (
                            <Input
                              value={editData?.phone || ''}
                              onChange={(e) => setEditData(prev => prev ? { ...prev, phone: e.target.value } : null)}
                              placeholder="Phone"
                              className="text-xs"
                            />
                          ) : (
                            contact.phone
                          )}
                        </TableCell>
                        <TableCell>
                          {editingIndex === index ? (
                            <div className="space-y-2">
                              <Input
                                value={editData?.address || ''}
                                onChange={(e) => setEditData(prev => prev ? { ...prev, address: e.target.value } : null)}
                                placeholder="Address"
                                className="text-xs"
                              />
                              <Input
                                value={editData?.city || ''}
                                onChange={(e) => setEditData(prev => prev ? { ...prev, city: e.target.value } : null)}
                                placeholder="City"
                                className="text-xs"
                              />
                            </div>
                          ) : (
                            <div>
                              {contact.address && <div>{contact.address}</div>}
                              {contact.city && <div>{contact.city}</div>}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          {editingIndex === index ? (
                            <div className="flex gap-1">
                              <Button size="sm" onClick={handleSave}>Save</Button>
                              <Button size="sm" variant="outline" onClick={handleCancel}>Cancel</Button>
                            </div>
                          ) : (
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEdit(index)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => handleDelete(index)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              
              <div className="flex justify-end mt-4">
                <Button 
                  onClick={onProceedToCustomize}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  Proceed to Customize Design
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DataReview;
