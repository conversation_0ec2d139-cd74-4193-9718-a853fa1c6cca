import React, { useState, useRef } from 'react';
import { Upload, Phone, Mail, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface VCardData {
  firstName: string;
  lastName: string;
  organization: string;
  jobTitle: string;
  address: string;
  city: string;
  phone: string;
  email: string;
  website?: string;
  phoneWork?: string;
  fax?: string;
  personalDescription?: string;
  photo?: string;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    youtube?: string;
    linkedin?: string;
    pinterest?: string;
    wechat?: string;
    whatsapp?: string;
    discord?: string;
    tumblr?: string;
    skype?: string;
    snapchat?: string;
    reddit?: string;
    medium?: string;
    quora?: string;
    yelp?: string;
    behance?: string;
  };
}

interface EnhancedManualEntryProps {
  currentVCard: VCardData;
  onVCardChange: (vcard: VCardData) => void;
  onSubmit: () => void;
}



const EnhancedManualEntry: React.FC<EnhancedManualEntryProps> = ({
  currentVCard,
  onVCardChange,
  onSubmit
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (field: keyof VCardData, value: string) => {
    onVCardChange({
      ...currentVCard,
      [field]: value
    });
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check file size (limit to 2MB for better QR code compatibility)
      if (file.size > 2 * 1024 * 1024) {
        alert('Please select an image smaller than 2MB for better QR code compatibility.');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;

        // Resize image for vCard compatibility
        resizeImageForVCard(result, (resizedImage) => {
          handleInputChange('photo', resizedImage);
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const resizeImageForVCard = (dataURL: string, callback: (resizedImage: string) => void) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        callback(dataURL);
        return;
      }

      // Resize to maximum 320x320 pixels for vCard compatibility
      const maxSize = 320;
      let { width, height } = img;

      if (width > height) {
        if (width > maxSize) {
          height = (height * maxSize) / width;
          width = maxSize;
        }
      } else {
        if (height > maxSize) {
          width = (width * maxSize) / height;
          height = maxSize;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress the image
      ctx.drawImage(img, 0, 0, width, height);

      // Convert to JPEG with 80% quality for smaller file size
      const resizedDataURL = canvas.toDataURL('image/jpeg', 0.8);
      callback(resizedDataURL);
    };

    img.onerror = () => {
      callback(dataURL); // Fallback to original if resize fails
    };

    img.src = dataURL;
  };

  const clearPhoto = () => {
    handleInputChange('photo', '');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <div className="inline-flex items-center gap-2 bg-gray-800 text-white px-4 py-2 rounded-lg">
          <span className="bg-white text-gray-800 px-2 py-1 rounded text-sm font-bold">STEP 2</span>
          <span>Complete the vCard data entry</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Basic Info */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Your Name</Label>
                <Input
                  id="name"
                  value={`${currentVCard.firstName} ${currentVCard.lastName}`.trim()}
                  onChange={(e) => {
                    const names = e.target.value.split(' ');
                    const firstName = names[0] || '';
                    const lastName = names.slice(1).join(' ') || '';
                    onVCardChange({
                      ...currentVCard,
                      firstName,
                      lastName
                    });
                  }}
placeholder=""
                />
              </div>

              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={currentVCard.website || ''}
                  onChange={(e) => handleInputChange('website', e.target.value)}
placeholder=""
                />
              </div>

              <div>
                <Label htmlFor="company">Company</Label>
                <Input
                  id="company"
                  value={currentVCard.organization}
                  onChange={(e) => handleInputChange('organization', e.target.value)}
placeholder=""
                />
              </div>

              <div>
                <Label htmlFor="jobTitle">Job Title</Label>
                <Input
                  id="jobTitle"
                  value={currentVCard.jobTitle}
                  onChange={(e) => handleInputChange('jobTitle', e.target.value)}
placeholder=""
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Contacts</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={currentVCard.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
placeholder=""
                  />
                </div>
                <div>
                  <Label htmlFor="phoneWork">Phone (Work)</Label>
                  <Input
                    id="phoneWork"
                    value={currentVCard.phoneWork || ''}
                    onChange={(e) => handleInputChange('phoneWork', e.target.value)}
placeholder=""
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="fax">Fax</Label>
                  <Input
                    id="fax"
                    value={currentVCard.fax || ''}
                    onChange={(e) => handleInputChange('fax', e.target.value)}
placeholder=""
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={currentVCard.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
placeholder=""
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Middle Column - Address & Description */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Address</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value="Philippines"
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={currentVCard.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
placeholder=""
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="address">Full Address</Label>
                <Input
                  id="address"
                  value={currentVCard.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
placeholder=""
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="state">State/Province</Label>
                  <Input
                    id="state"
  placeholder=""
                  />
                </div>
                <div>
                  <Label htmlFor="zipCode">Zip Code</Label>
                  <Input
                    id="zipCode"
  placeholder=""
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Personal Description</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={currentVCard.personalDescription || ''}
                onChange={(e) => handleInputChange('personalDescription', e.target.value)}
placeholder=""
                className="min-h-[100px]"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Primary Photo</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600">
                <div>📸 Recommended: 320 x 320 px, under 2MB</div>
                <div className="text-blue-600 mt-1">✨ Photo will be embedded in QR code and appear in phonebook when saved!</div>
                <div className="text-green-600 text-xs mt-1">
                  🔄 Images are automatically resized and optimized for vCard compatibility
                </div>
              </div>
              
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                {currentVCard.photo ? (
                  <div className="space-y-4">
                    <img 
                      src={currentVCard.photo} 
                      alt="Profile" 
                      className="w-24 h-24 rounded-full mx-auto object-cover"
                    />
                    <div className="space-y-2">
                      <Button 
                        onClick={() => fileInputRef.current?.click()}
                        className="w-full bg-blue-500 hover:bg-blue-600"
                      >
                        Upload
                      </Button>
                      <Button 
                        onClick={clearPhoto}
                        variant="outline"
                        className="w-full"
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto flex items-center justify-center">
                      <Upload className="w-8 h-8 text-gray-400" />
                    </div>
                    <Button 
                      onClick={() => fileInputRef.current?.click()}
                      className="w-full bg-blue-500 hover:bg-blue-600"
                    >
                      Upload
                    </Button>
                    <Button 
                      variant="outline"
                      className="w-full"
                      disabled
                    >
                      Clear
                    </Button>
                  </div>
                )}
                
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handlePhotoUpload}
                  className="hidden"
                  title="Upload profile photo"
                  aria-label="Upload profile photo for vCard"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Preview & Social Media */}
        <div className="space-y-6">
          {/* Mobile Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Mobile Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mx-auto w-64 h-96 bg-gradient-to-b from-blue-500 to-blue-800 rounded-3xl p-4 text-white relative overflow-hidden">
                <div className="text-center space-y-4">
                  <div className="w-20 h-20 bg-white rounded-full mx-auto overflow-hidden">
                    {currentVCard.photo ? (
                      <img 
                        src={currentVCard.photo} 
                        alt="Profile" 
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-400 text-xs">Photo</span>
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <h3 className="font-bold text-lg">
                      {currentVCard.firstName || currentVCard.lastName 
                        ? `${currentVCard.firstName} ${currentVCard.lastName}`.trim()
                        : 'Your Name'
                      }
                    </h3>
                    <p className="text-sm opacity-90">
                      {currentVCard.jobTitle || 'Job Title'}
                    </p>
                    <p className="text-xs opacity-75">
                      {currentVCard.organization || 'Company'}
                    </p>
                  </div>

                  <div className="text-xs opacity-90">
                    {currentVCard.personalDescription || 'Add a personal description'}
                  </div>

                  <div className="flex justify-center space-x-2">
                    <div className="bg-white bg-opacity-20 rounded-lg p-2 flex items-center justify-center">
                      <Phone className="w-4 h-4" />
                    </div>
                    <div className="bg-white bg-opacity-20 rounded-lg p-2 flex items-center justify-center">
                      <Mail className="w-4 h-4" />
                    </div>
                    <div className="bg-white bg-opacity-20 rounded-lg p-2 flex items-center justify-center">
                      <Globe className="w-4 h-4" />
                    </div>
                  </div>

                  <div className="space-y-2 text-xs">
                    <div className="flex items-center justify-between bg-white bg-opacity-10 rounded p-2">
                      <span>Phone (Work)</span>
                      <span>{currentVCard.phoneWork || 'Add work phone'}</span>
                    </div>
                    <div className="flex items-center justify-between bg-white bg-opacity-10 rounded p-2">
                      <span>Phone</span>
                      <span>{currentVCard.phone || 'Add phone number'}</span>
                    </div>
                  </div>

                  <Button 
                    size="sm" 
                    className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6"
                  >
                    Save to contacts
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>



      {/* Proceed Button */}
      <div className="text-center">
        <Button
          onClick={onSubmit}
          className="bg-green-500 hover:bg-green-600 text-white px-8 py-3 text-lg"
          size="lg"
        >
          Proceed with Customize Design
        </Button>
      </div>
    </div>
  );
};

export default EnhancedManualEntry;
