import React, { useState, useRef } from 'react';
import { Upload, X, Facebook, Instagram, Twitter, Youtube, Linkedin, MessageCircle, Phone, Mail, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface VCardData {
  firstName: string;
  lastName: string;
  organization: string;
  jobTitle: string;
  address: string;
  city: string;
  phone: string;
  email: string;
  website?: string;
  phoneWork?: string;
  fax?: string;
  personalDescription?: string;
  photo?: string;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    youtube?: string;
    linkedin?: string;
    pinterest?: string;
    wechat?: string;
    whatsapp?: string;
    discord?: string;
    tumblr?: string;
    skype?: string;
    snapchat?: string;
    reddit?: string;
    medium?: string;
    quora?: string;
    yelp?: string;
    behance?: string;
  };
}

interface EnhancedManualEntryProps {
  currentVCard: VCardData;
  onVCardChange: (vcard: VCardData) => void;
  onSubmit: () => void;
}

const socialMediaIcons = {
  facebook: Facebook,
  instagram: Instagram,
  twitter: Twitter,
  youtube: Youtube,
  linkedin: Linkedin,
  whatsapp: MessageCircle,
  discord: MessageCircle,
  pinterest: MessageCircle,
  wechat: MessageCircle,
  tumblr: MessageCircle,
  skype: MessageCircle,
  snapchat: MessageCircle,
  reddit: MessageCircle,
  medium: MessageCircle,
  quora: MessageCircle,
  yelp: MessageCircle,
  behance: MessageCircle,
};

const EnhancedManualEntry: React.FC<EnhancedManualEntryProps> = ({
  currentVCard,
  onVCardChange,
  onSubmit
}) => {
  const [backgroundType, setBackgroundType] = useState<'solid' | 'gradient'>('gradient');
  const [gradientColors, setGradientColors] = useState(['#4a87e0', '#000080']);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (field: keyof VCardData, value: string) => {
    onVCardChange({
      ...currentVCard,
      [field]: value
    });
  };

  const handleSocialMediaChange = (platform: string, value: string) => {
    onVCardChange({
      ...currentVCard,
      socialMedia: {
        ...currentVCard.socialMedia,
        [platform]: value
      }
    });
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        handleInputChange('photo', result);
      };
      reader.readAsDataURL(file);
    }
  };

  const clearPhoto = () => {
    handleInputChange('photo', '');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <div className="inline-flex items-center gap-2 bg-gray-800 text-white px-4 py-2 rounded-lg">
          <span className="bg-white text-gray-800 px-2 py-1 rounded text-sm font-bold">STEP 2</span>
          <span>Complete the vCard data entry</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Basic Info */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Your Name</Label>
                <Input
                  id="name"
                  value={`${currentVCard.firstName} ${currentVCard.lastName}`.trim()}
                  onChange={(e) => {
                    const names = e.target.value.split(' ');
                    const firstName = names[0] || '';
                    const lastName = names.slice(1).join(' ') || '';
                    onVCardChange({
                      ...currentVCard,
                      firstName,
                      lastName
                    });
                  }}
                  placeholder="David Jara"
                />
              </div>

              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={currentVCard.website || ''}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://cathedralofpraisemanila.com.ph"
                />
              </div>

              <div>
                <Label htmlFor="company">Company</Label>
                <Input
                  id="company"
                  value={currentVCard.organization}
                  onChange={(e) => handleInputChange('organization', e.target.value)}
                  placeholder="Cathedral Of Praise"
                />
              </div>

              <div>
                <Label htmlFor="jobTitle">Job Title</Label>
                <Input
                  id="jobTitle"
                  value={currentVCard.jobTitle}
                  onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                  placeholder="Head of Technology & Digital Innovation"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Contacts</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={currentVCard.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="09088191381"
                  />
                </div>
                <div>
                  <Label htmlFor="phoneWork">Phone (Work)</Label>
                  <Input
                    id="phoneWork"
                    value={currentVCard.phoneWork || ''}
                    onChange={(e) => handleInputChange('phoneWork', e.target.value)}
                    placeholder="Phone (Work)"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="fax">Fax</Label>
                  <Input
                    id="fax"
                    value={currentVCard.fax || ''}
                    onChange={(e) => handleInputChange('fax', e.target.value)}
                    placeholder="Fax"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={currentVCard.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Middle Column - Address & Description */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Address</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value="Philippines"
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={currentVCard.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    placeholder="Metro Manila"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="address">Full Address</Label>
                <Input
                  id="address"
                  value={currentVCard.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="350 Taft Ave, Ermita, Manila, 1000 Metro Manila"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="state">State/Province</Label>
                  <Input
                    id="state"
                    placeholder="Manila"
                  />
                </div>
                <div>
                  <Label htmlFor="zipCode">Zip Code</Label>
                  <Input
                    id="zipCode"
                    placeholder="1000"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Personal Description</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={currentVCard.personalDescription || ''}
                onChange={(e) => handleInputChange('personalDescription', e.target.value)}
                placeholder="Hello! I am happy to be of service"
                className="min-h-[100px]"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Primary Photo</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600">320 x 320 px, 72dpi</div>
              
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                {currentVCard.photo ? (
                  <div className="space-y-4">
                    <img 
                      src={currentVCard.photo} 
                      alt="Profile" 
                      className="w-24 h-24 rounded-full mx-auto object-cover"
                    />
                    <div className="space-y-2">
                      <Button 
                        onClick={() => fileInputRef.current?.click()}
                        className="w-full bg-blue-500 hover:bg-blue-600"
                      >
                        Upload
                      </Button>
                      <Button 
                        onClick={clearPhoto}
                        variant="outline"
                        className="w-full"
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto flex items-center justify-center">
                      <Upload className="w-8 h-8 text-gray-400" />
                    </div>
                    <Button 
                      onClick={() => fileInputRef.current?.click()}
                      className="w-full bg-blue-500 hover:bg-blue-600"
                    >
                      Upload
                    </Button>
                    <Button 
                      variant="outline"
                      className="w-full"
                      disabled
                    >
                      Clear
                    </Button>
                  </div>
                )}
                
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handlePhotoUpload}
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Preview & Social Media */}
        <div className="space-y-6">
          {/* Mobile Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Mobile Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mx-auto w-64 h-96 bg-gradient-to-b from-blue-500 to-blue-800 rounded-3xl p-4 text-white relative overflow-hidden">
                <div className="text-center space-y-4">
                  <div className="w-20 h-20 bg-white rounded-full mx-auto overflow-hidden">
                    {currentVCard.photo ? (
                      <img 
                        src={currentVCard.photo} 
                        alt="Profile" 
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-400 text-xs">Photo</span>
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <h3 className="font-bold text-lg">
                      {currentVCard.firstName || currentVCard.lastName 
                        ? `${currentVCard.firstName} ${currentVCard.lastName}`.trim()
                        : 'Your Name'
                      }
                    </h3>
                    <p className="text-sm opacity-90">
                      {currentVCard.jobTitle || 'Job Title'}
                    </p>
                    <p className="text-xs opacity-75">
                      {currentVCard.organization || 'Company'}
                    </p>
                  </div>

                  <div className="text-xs opacity-90">
                    {currentVCard.personalDescription || 'Personal description will appear here'}
                  </div>

                  <div className="flex justify-center space-x-2">
                    <div className="bg-white bg-opacity-20 rounded-lg p-2 flex items-center justify-center">
                      <Phone className="w-4 h-4" />
                    </div>
                    <div className="bg-white bg-opacity-20 rounded-lg p-2 flex items-center justify-center">
                      <Mail className="w-4 h-4" />
                    </div>
                    <div className="bg-white bg-opacity-20 rounded-lg p-2 flex items-center justify-center">
                      <Globe className="w-4 h-4" />
                    </div>
                  </div>

                  <div className="space-y-2 text-xs">
                    <div className="flex items-center justify-between bg-white bg-opacity-10 rounded p-2">
                      <span>Phone (Work)</span>
                      <span>{currentVCard.phoneWork || 'Phone (Work)'}</span>
                    </div>
                    <div className="flex items-center justify-between bg-white bg-opacity-10 rounded p-2">
                      <span>Phone</span>
                      <span>{currentVCard.phone || 'Phone'}</span>
                    </div>
                  </div>

                  <Button 
                    size="sm" 
                    className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6"
                  >
                    Save to contacts
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Background Color Section */}
      <Card>
        <CardHeader>
          <CardTitle>Background Color</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <RadioGroup
            value={backgroundType}
            onValueChange={(value: 'solid' | 'gradient') => setBackgroundType(value)}
            className="flex space-x-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="solid" id="solid" />
              <Label htmlFor="solid">Solid Color</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="gradient" id="gradient" />
              <Label htmlFor="gradient">Gradient</Label>
            </div>
          </RadioGroup>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <input
                type="color"
                value={gradientColors[0]}
                onChange={(e) => setGradientColors([e.target.value, gradientColors[1]])}
                className="w-12 h-8 rounded border"
                title="Primary gradient color"
                aria-label="Primary gradient color"
              />
              <span className="text-sm">{gradientColors[0]}</span>
            </div>

            <span>—</span>

            <div className="flex items-center space-x-2">
              <input
                type="color"
                value={gradientColors[1]}
                onChange={(e) => setGradientColors([gradientColors[0], e.target.value])}
                className="w-12 h-8 rounded border"
                title="Secondary gradient color"
                aria-label="Secondary gradient color"
              />
              <span className="text-sm">{gradientColors[1]}</span>
            </div>
          </div>

          <div>
            <Label>Gradient Type</Label>
            <div className="flex space-x-2 mt-2">
              {['linear', 'radial', 'conic', 'diamond', 'square'].map((type, index) => (
                <div
                  key={type}
                  className={`w-8 h-8 rounded-full border-2 border-gray-300 cursor-pointer ${
                    index === 0 ? 'bg-gradient-to-r' :
                    index === 1 ? 'bg-gradient-radial' :
                    'bg-gradient-to-br'
                  }`}
                  title={`${type} gradient`}
                />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Social Media Links */}
      <Card>
        <CardHeader>
          <CardTitle>Add Social Media Links</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-6 gap-4">
            {Object.entries(socialMediaIcons).map(([platform, Icon]) => (
              <div key={platform} className="text-center">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-2 cursor-pointer hover:bg-gray-200">
                  <Icon className="w-6 h-6 text-gray-600" />
                </div>
                <Input
                  placeholder={`${platform} URL`}
                  value={currentVCard.socialMedia?.[platform as keyof typeof currentVCard.socialMedia] || ''}
                  onChange={(e) => handleSocialMediaChange(platform, e.target.value)}
                  className="text-xs"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Generate Button */}
      <div className="text-center">
        <Button
          onClick={onSubmit}
          className="bg-green-500 hover:bg-green-600 text-white px-8 py-3 text-lg"
          size="lg"
        >
          Generate dynamic QR code
        </Button>
        <p className="text-sm text-blue-600 mt-2">
          💡 We only provide dynamic QR codes for vCards since they contain a lot of data.
        </p>
      </div>
    </div>
  );
};

export default EnhancedManualEntry;
