
import React, { useCallback, useState } from 'react';
import { Upload, FileText, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import <PERSON> from 'papaparse';

interface VCardData {
  firstName: string;
  lastName: string;
  organization: string;
  jobTitle: string;
  address: string;
  city: string;
  phone: string;
  email: string;
  website: string;
}

interface CSVImporterProps {
  onDataImported: (data: VCardData[]) => void;
}

const CSVImporter: React.FC<CSVImporterProps> = ({ onDataImported }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [importedCount, setImportedCount] = useState(0);
  const { toast } = useToast();

  const processCSVData = (results: Papa.ParseResult<any>) => {
    try {
      const data = results.data;
      const headers = data[0] as string[];
      
      // Map common header variations to our expected format
      const headerMapping: Record<string, string> = {
        'first name': 'firstName',
        'firstname': 'firstName',
        'fname': 'firstName',
        'last name': 'lastName',
        'lastname': 'lastName',
        'lname': 'lastName',
        'company': 'organization',
        'org': 'organization',
        'organization': 'organization',
        'job title': 'jobTitle',
        'jobtitle': 'jobTitle',
        'title': 'jobTitle',
        'position': 'jobTitle',
        'role': 'jobTitle',
        'address': 'address',
        'street': 'address',
        'street address': 'address',
        'city': 'city',
        'phone': 'phone',
        'telephone': 'phone',
        'mobile': 'phone',
        'email': 'email',
        'email address': 'email',
        'website': 'website',
        'url': 'website',
        'web': 'website'
      };

      const mappedHeaders = headers.map(header => 
        headerMapping[header.toLowerCase().trim()] || header.toLowerCase().trim()
      );

      const vcardData: VCardData[] = data.slice(1)
        .filter((row: any[]) => row.some(cell => cell && cell.toString().trim()))
        .map((row: any[]) => {
          const contact: VCardData = {
            firstName: '',
            lastName: '',
            organization: '',
            jobTitle: '',
            address: '',
            city: '',
            phone: '',
            email: '',
            website: ''
          };

          mappedHeaders.forEach((header, index) => {
            const value = row[index]?.toString().trim() || '';
            if (header in contact) {
              (contact as any)[header] = value;
            }
          });

          return contact;
        })
        .filter(contact => contact.firstName || contact.lastName || contact.email);

      return vcardData;
    } catch (error) {
      console.error('Error processing CSV data:', error);
      throw new Error('Failed to process CSV data');
    }
  };

  const handleFileUpload = useCallback((file: File) => {
    if (!file.name.toLowerCase().endsWith('.csv')) {
      toast({
        title: "Invalid File Type",
        description: "Please upload a CSV file.",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);

    Papa.parse(file, {
      complete: (results) => {
        try {
          const vcardData = processCSVData(results);
          setImportedCount(vcardData.length);
          onDataImported(vcardData);
          
          toast({
            title: "CSV Imported Successfully",
            description: `Imported ${vcardData.length} contact(s) for QR code generation.`,
          });
        } catch (error) {
          toast({
            title: "Import Failed",
            description: "Failed to parse CSV file. Please check the format.",
            variant: "destructive"
          });
        } finally {
          setIsProcessing(false);
        }
      },
      error: (error) => {
        console.error('Papa Parse error:', error);
        toast({
          title: "Parse Error",
          description: "Failed to read CSV file.",
          variant: "destructive"
        });
        setIsProcessing(false);
      }
    });
  }, [onDataImported, toast]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  }, [handleFileUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files[0]);
    }
  }, [handleFileUpload]);

  return (
    <div className="space-y-4">
      <div
        className={`
          border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200
          ${isDragOver 
            ? 'border-primary bg-primary/5 scale-105' 
            : 'border-muted-foreground/25 hover:border-primary/50'
          }
          ${isProcessing ? 'opacity-50 pointer-events-none' : ''}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="space-y-4">
          {importedCount > 0 ? (
            <div className="flex flex-col items-center space-y-2">
              <CheckCircle className="h-12 w-12 text-green-500" />
              <p className="text-lg font-medium text-green-700">
                {importedCount} contacts imported successfully!
              </p>
              <p className="text-sm text-muted-foreground">
                Ready for QR code generation
              </p>
            </div>
          ) : (
            <>
              <div className="flex justify-center">
                {isProcessing ? (
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                ) : (
                  <Upload className={`h-12 w-12 ${isDragOver ? 'text-primary' : 'text-muted-foreground'}`} />
                )}
              </div>
              
              <div>
                <p className="text-lg font-medium">
                  {isProcessing ? 'Processing CSV...' : 'Drop your CSV file here'}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  or click to browse files
                </p>
              </div>
            </>
          )}
          
          <input
            type="file"
            accept=".csv"
            onChange={handleFileSelect}
            className="hidden"
            id="csv-upload"
            disabled={isProcessing}
          />
          
          {!isProcessing && (
            <Button 
              variant="outline" 
              asChild
              className="hover:bg-primary hover:text-primary-foreground transition-colors"
            >
              <label htmlFor="csv-upload" className="cursor-pointer">
                <FileText className="h-4 w-4 mr-2" />
                Select CSV File
              </label>
            </Button>
          )}
        </div>
      </div>

      <div className="text-xs text-muted-foreground space-y-1">
        <p><strong>Expected CSV format:</strong></p>
        <p>Headers: firstName, lastName, organization, jobTitle, address, city, phone, email</p>
        <p>Alternative headers are automatically mapped (e.g., "First Name", "Job Title", "Company", etc.)</p>
      </div>
    </div>
  );
};

export default CSVImporter;
