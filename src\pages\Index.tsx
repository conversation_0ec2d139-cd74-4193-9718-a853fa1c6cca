import React, { useState, useRef, useEffect } from 'react';
import { Upload, Download, QrCode, Palette, Image as ImageIcon, UserCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import CSVImporter from '@/components/CSVImporter';
import QRCustomizer from '@/components/QRCustomizer';
import QRPreview from '@/components/QRPreview';

import DataReview from '@/components/DataReview';
import EnhancedManualEntry from '@/components/EnhancedManualEntry';
import { useToast } from '@/hooks/use-toast';
import JSZip from 'jszip';
import QRCode from 'qrcode';

interface VCardData {
  firstName: string;
  lastName: string;
  organization: string;
  jobTitle: string;
  address: string;
  city: string;
  phone: string;
  email: string;
  website?: string;
  phoneWork?: string;
  fax?: string;
  personalDescription?: string;
  photo?: string;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    youtube?: string;
    linkedin?: string;
    pinterest?: string;
    wechat?: string;
    whatsapp?: string;
    discord?: string;
    tumblr?: string;
    skype?: string;
    snapchat?: string;
    reddit?: string;
    medium?: string;
    quora?: string;
    yelp?: string;
    behance?: string;
  };
}

interface QRDesignSettings {
  foregroundColor: string;
  backgroundColor: string;
  style: 'square' | 'rounded' | 'dots';
  logo?: string;
  logoSize: number;
  pattern?: string;
  eyeStyle?: string;
  eyeFrameStyle?: string;
  eyeBallStyle?: string;
  frameStyle?: string;
  frameText?: string;
  frameFont?: string;
  template?: string;
  gradientType?: 'solid' | 'gradient';
}

const Index = () => {
  const [vcardData, setVcardData] = useState<VCardData[]>([]);
  const [currentVCard, setCurrentVCard] = useState<VCardData>({
    firstName: 'John',
    lastName: 'Doe',
    organization: 'Tech Company',
    jobTitle: 'Software Developer',
    address: '123 Main St',
    city: 'New York',
    phone: '******-123-4567',
    email: '<EMAIL>',
    website: 'https://johndoe.com',
    phoneWork: '',
    fax: '',
    personalDescription: '',
    photo: '',
    socialMedia: {
      facebook: '',
      instagram: '',
      twitter: '',
      youtube: '',
      linkedin: '',
      pinterest: '',
      wechat: '',
      whatsapp: '',
      discord: '',
      tumblr: '',
      skype: '',
      snapchat: '',
      reddit: '',
      medium: '',
      quora: '',
      yelp: '',
      behance: '',
    },
  });
  const [designSettings, setDesignSettings] = useState<QRDesignSettings>({
    foregroundColor: '#000000',
    backgroundColor: '#ffffff',
    style: 'square',
    logoSize: 20,
    pattern: 'square',
    eyeStyle: 'square',
    eyeFrameStyle: 'square',
    eyeBallStyle: 'square',
    frameStyle: 'none',
    frameText: 'SCAN ME',
    frameFont: 'Arial',
    template: 'none',
    gradientType: 'solid'
  });
  const [activeTab, setActiveTab] = useState('import');
  const [isGeneratingZip, setIsGeneratingZip] = useState(false);
  const [generatedQRCodes, setGeneratedQRCodes] = useState<string[]>([]);
  const [isGeneratingQRs, setIsGeneratingQRs] = useState(false);
  const { toast } = useToast();

  // Function to optimize photo for vCard embedding
  const optimizePhotoForVCard = (photoDataURL: string): string | null => {
    try {
      // If it's already a data URL, extract the base64 part
      if (photoDataURL.startsWith('data:image/')) {
        const base64Data = photoDataURL.split(',')[1];

        // For vCard compatibility, we'll use the image as-is but ensure it's properly formatted
        // The image should already be optimized from the upload process
        // Remove any whitespace and newlines for vCard format
        return base64Data.replace(/\s/g, '');
      }
      return null;
    } catch (error) {
      console.error('Error optimizing photo for vCard:', error);
      return null;
    }
  };

  const generateVCardString = (data: VCardData): string => {
    // Helper function to escape vCard values
    const escapeVCardValue = (value: string) => {
      if (!value) return '';
      return value.replace(/\n/g, '\\n').replace(/,/g, '\\,').replace(/;/g, '\\;');
    };

    const fullAddress = [data.address, data.city].filter(Boolean).join(', ');
    const fullName = `${data.firstName || ''} ${data.lastName || ''}`.trim();

    let vcard = `BEGIN:VCARD
VERSION:3.0
FN:${escapeVCardValue(fullName)}
N:${escapeVCardValue(data.lastName || '')};${escapeVCardValue(data.firstName || '')};;;
ORG:${escapeVCardValue(data.organization || '')}
TITLE:${escapeVCardValue(data.jobTitle || '')}`;

    // Add phone only if it exists
    if (data.phone) {
      vcard += `\nTEL:${escapeVCardValue(data.phone)}`;
    }

    // Add email only if it exists
    if (data.email) {
      vcard += `\nEMAIL:${escapeVCardValue(data.email)}`;
    }

    // Add address only if it exists
    if (fullAddress) {
      vcard += `\nADR:;;${escapeVCardValue(fullAddress)};;;;`;
    }

    // Add optional fields
    if (data.website) {
      vcard += `\nURL:${escapeVCardValue(data.website)}`;
    }
    if (data.phoneWork) {
      vcard += `\nTEL;TYPE=WORK:${escapeVCardValue(data.phoneWork)}`;
    }
    if (data.fax) {
      vcard += `\nTEL;TYPE=FAX:${escapeVCardValue(data.fax)}`;
    }
    if (data.personalDescription) {
      vcard += `\nNOTE:${escapeVCardValue(data.personalDescription)}`;
    }

    // Add photo if it exists (optimized for vCard)
    if (data.photo) {
      // Convert photo to optimized format for vCard
      const optimizedPhoto = optimizePhotoForVCard(data.photo);
      if (optimizedPhoto) {
        vcard += `\nPHOTO;ENCODING=BASE64;TYPE=JPEG:${optimizedPhoto}`;
      }
    }

    // Add social media links
    if (data.socialMedia) {
      Object.entries(data.socialMedia).forEach(([platform, url]) => {
        if (url && url.trim()) {
          vcard += `\nX-SOCIALPROFILE;TYPE=${platform.toUpperCase()}:${escapeVCardValue(url)}`;
        }
      });
    }

    vcard += '\nEND:VCARD';
    return vcard;
  };

  const handleCSVImport = (data: VCardData[]) => {
    setVcardData(data);
    if (data.length > 0) {
      setCurrentVCard(data[0]);
      setActiveTab('review');
      toast({
        title: "CSV Imported Successfully",
        description: `Imported ${data.length} contact(s) for review.`,
      });
    }
  };

  const handleManualEntry = () => {
    if (currentVCard.firstName || currentVCard.lastName || currentVCard.email) {
      const newData = [...vcardData, currentVCard];
      setVcardData(newData);
      setCurrentVCard({
        firstName: '',
        lastName: '',
        organization: '',
        jobTitle: '',
        address: '',
        city: '',
        phone: '',
        email: '',
        website: '',
        phoneWork: '',
        fax: '',
        personalDescription: '',
        photo: '',
        socialMedia: {
          facebook: '',
          instagram: '',
          twitter: '',
          youtube: '',
          linkedin: '',
          pinterest: '',
          wechat: '',
          whatsapp: '',
          discord: '',
          tumblr: '',
          skype: '',
          snapchat: '',
          reddit: '',
          medium: '',
          quora: '',
          yelp: '',
          behance: '',
        },
      });
      setActiveTab('review');
      toast({
        title: "Contact Added",
        description: "Contact added for review.",
      });
    }
  };

  const handleDesignChange = (newSettings: Partial<QRDesignSettings>) => {
    setDesignSettings(prev => ({ ...prev, ...newSettings }));
  };

  const generateQRCodeDataURL = async (vCardString: string, settings: QRDesignSettings): Promise<string> => {
    return new Promise((resolve, reject) => {
      // Validate input
      if (!vCardString || !vCardString.trim()) {
        reject(new Error('vCard string is empty'));
        return;
      }

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      QRCode.toCanvas(canvas, vCardString, {
        width: 1000,
        margin: 4,
        color: {
          dark: settings.foregroundColor || '#000000',
          light: settings.backgroundColor || '#ffffff',
        },
        errorCorrectionLevel: 'M',
      }).then(() => {
        if (settings.logo) {
          const img = new Image();
          img.onload = () => {
            const logoSizePixels = (canvas.width * settings.logoSize) / 100;
            const x = (canvas.width - logoSizePixels) / 2;
            const y = (canvas.height - logoSizePixels) / 2;

            ctx.fillStyle = settings.backgroundColor;
            ctx.beginPath();
            ctx.arc(
              canvas.width / 2, 
              canvas.height / 2, 
              logoSizePixels / 2 + 8,
              0, 
              2 * Math.PI
            );
            ctx.fill();

            ctx.drawImage(img, x, y, logoSizePixels, logoSizePixels);
            resolve(canvas.toDataURL('image/png'));
          };
          img.onerror = () => resolve(canvas.toDataURL('image/png'));
          img.src = settings.logo;
        } else {
          resolve(canvas.toDataURL('image/png'));
        }
      }).catch(reject);
    });
  };

  const generateAllQRCodes = async () => {
    // Check if we have data in vcardData array or currentVCard
    const hasVCardData = vcardData.length > 0;
    const hasCurrentVCard = currentVCard.firstName || currentVCard.lastName || currentVCard.email;

    if (!hasVCardData && !hasCurrentVCard) {
      toast({
        title: "No Data",
        description: "Please add contact data first using CSV import or manual entry.",
        variant: "destructive"
      });
      return;
    }

    // If we only have currentVCard data, add it to vcardData first
    let dataToProcess = [...vcardData];
    if (!hasVCardData && hasCurrentVCard) {
      dataToProcess = [currentVCard];
      setVcardData(dataToProcess);
    }

    setIsGeneratingQRs(true);
    toast({
      title: "Generating QR Codes",
      description: `Generating ${dataToProcess.length} QR codes...`,
    });

    try {
      const qrCodes: string[] = [];

      for (let i = 0; i < dataToProcess.length; i++) {
        const contact = dataToProcess[i];
        const vCardString = generateVCardString(contact);
        console.log('Generated vCard string:', vCardString); // Debug log
        const dataURL = await generateQRCodeDataURL(vCardString, designSettings);
        qrCodes.push(dataURL);
      }

      setGeneratedQRCodes(qrCodes);
      setActiveTab('preview');

      toast({
        title: "QR Codes Generated",
        description: `Successfully generated ${qrCodes.length} high-resolution QR codes.`,
      });
    } catch (error) {
      console.error('Error generating QR codes:', error);
      toast({
        title: "Generation Failed",
        description: `Failed to generate QR codes. Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });
    } finally {
      setIsGeneratingQRs(false);
    }
  };

  const downloadSingleQRCode = async (index?: number) => {
    let contact: VCardData;
    let dataURL: string;

    if (index !== undefined && generatedQRCodes[index]) {
      // Download from already generated QR codes
      contact = vcardData[index];
      dataURL = generatedQRCodes[index];
    } else {
      // Generate new QR code for current contact
      contact = vcardData[0] || currentVCard;
      if (!contact.firstName && !contact.lastName && !contact.email) {
        toast({
          title: "No Data",
          description: "Please add contact data first.",
          variant: "destructive"
        });
        return;
      }

      try {
        const vCardString = generateVCardString(contact);
        dataURL = await generateQRCodeDataURL(vCardString, designSettings);
      } catch (error) {
        console.error('Error generating QR code:', error);
        toast({
          title: "Generation Failed",
          description: "Failed to generate QR code. Please try again.",
          variant: "destructive"
        });
        return;
      }
    }

    try {
      // Convert data URL to blob
      const base64Data = dataURL.split(',')[1];
      const byteCharacters = atob(base64Data);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: 'image/png' });

      // Create filename with proper extension
      const baseName = `${contact.firstName || 'Unknown'}_${contact.lastName || 'Contact'}_QR`
        .replace(/[^a-z0-9_]/gi, '_')
        .replace(/_+/g, '_')
        .replace(/^_|_$/g, '');
      const fileName = `${baseName}.png`;

      // Download the file
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Download Complete",
        description: `Successfully downloaded ${fileName} (1000x1000).`,
      });
    } catch (error) {
      console.error('Error downloading QR code:', error);
      toast({
        title: "Download Failed",
        description: "Failed to download QR code. Please try again.",
        variant: "destructive"
      });
    }
  };

  const downloadAllQRCodes = async () => {
    if (generatedQRCodes.length === 0) {
      toast({
        title: "No QR Codes",
        description: "Please generate QR codes first.",
        variant: "destructive"
      });
      return;
    }

    setIsGeneratingZip(true);
    toast({
      title: "Preparing Download",
      description: `Preparing ${generatedQRCodes.length} high-resolution QR codes for download...`,
    });

    try {
      const zip = new JSZip();
      
      for (let i = 0; i < generatedQRCodes.length; i++) {
        const contact = vcardData[i];
        const dataURL = generatedQRCodes[i];

        // Convert data URL to blob properly
        const base64Data = dataURL.split(',')[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let j = 0; j < byteCharacters.length; j++) {
          byteNumbers[j] = byteCharacters.charCodeAt(j);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'image/png' });

        // Create filename with proper extension
        const baseName = `${contact.firstName || 'Unknown'}_${contact.lastName || 'Contact'}_QR_${i + 1}`
          .replace(/[^a-z0-9_]/gi, '_')
          .replace(/_+/g, '_')
          .replace(/^_|_$/g, '');
        const fileName = `${baseName}.png`;

        zip.file(fileName, blob);
      }

      const zipBlob = await zip.generateAsync({ type: 'blob' });
      
      const url = URL.createObjectURL(zipBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `vcard_qr_codes_${vcardData.length}_contacts_1000x1000.zip`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Download Complete",
        description: `Successfully downloaded ${vcardData.length} high-resolution (1000x1000) QR codes with proper .png extensions.`,
      });
    } catch (error) {
      console.error('Error generating ZIP:', error);
      toast({
        title: "Download Failed",
        description: "Failed to create download. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingZip(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl">
              <QrCode className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              COP VCARD QR Generator by David Jara
            </h1>
          </div>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Create professional QR codes for contact information with bulk CSV import, enhanced manual entry with photo upload, social media links, and advanced customization options.
          </p>
        </div>

        <div className="max-w-7xl mx-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-5 mb-8">
              <TabsTrigger value="import" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                Import Data
              </TabsTrigger>
              <TabsTrigger value="manual" className="flex items-center gap-2">
                <UserCheck className="h-4 w-4" />
                Manual Entry
              </TabsTrigger>
              <TabsTrigger value="review" className="flex items-center gap-2">
                <UserCheck className="h-4 w-4" />
                Review Data
              </TabsTrigger>
              <TabsTrigger value="customize" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Customize Design
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <QrCode className="h-4 w-4" />
                Preview & Download
              </TabsTrigger>
            </TabsList>

            <TabsContent value="import" className="space-y-6">
              <div className="flex justify-center">
                <Card className="border-2 border-dashed border-muted hover:border-primary/50 transition-colors max-w-2xl w-full">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 justify-center">
                      <Upload className="h-5 w-5" />
                      Bulk Import from CSV
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CSVImporter onDataImported={handleCSVImport} />
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="manual" className="space-y-6">
              <EnhancedManualEntry
                currentVCard={currentVCard}
                onVCardChange={setCurrentVCard}
                onSubmit={handleManualEntry}
              />
            </TabsContent>

            <TabsContent value="review" className="space-y-6">
              <DataReview 
                data={vcardData}
                onDataChange={setVcardData}
                onProceedToCustomize={() => setActiveTab('customize')}
              />
            </TabsContent>

            <TabsContent value="customize" className="space-y-6">
              {/* Step 3 Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                  <div className="bg-gray-800 text-white px-3 py-1 rounded text-sm font-bold">
                    STEP 3
                  </div>
                  <span className="text-lg font-medium">Customize your QR</span>
                </div>
                <div className="text-right text-sm text-gray-600">
                  <div>
                    <span className="text-blue-500 underline cursor-pointer">Why is my QR code not working?</span>
                  </div>
                  <div>You can customize these templates later</div>
                  <div>to match your brand</div>
                  <div>Always scan to test that your QR code works</div>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Side - QR Customizer */}
                <div>
                  <QRCustomizer
                    settings={designSettings}
                    onSettingsChange={handleDesignChange}
                  />
                </div>

                {/* Right Side - Preview and Download */}
                <div className="space-y-6">
                  {/* QR Preview */}
                  <div className="flex justify-center">
                    <QRPreview
                      vCardString={generateVCardString(vcardData[0] || currentVCard)}
                      settings={designSettings}
                      size={300}
                    />
                  </div>

                  {/* Download Options */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <input type="checkbox" id="saveTemplate" className="rounded" />
                      <label htmlFor="saveTemplate" className="text-sm text-gray-600">
                        Save as a template
                      </label>
                    </div>

                    {/* Format Selection */}
                    <div className="flex items-center gap-6">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-green-500 border-2 border-green-500"></div>
                        <span className="text-sm font-medium">PNG</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full border-2 border-gray-300"></div>
                        <span className="text-sm text-gray-600">SVG</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full border-2 border-gray-300"></div>
                        <span className="text-sm text-gray-600">PDF</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full border-2 border-gray-300"></div>
                        <span className="text-sm text-gray-600">EPS</span>
                      </div>
                    </div>

                    {/* Generate QR Codes Button */}
                    <Button
                      className="w-full bg-green-500 hover:bg-green-600 text-white py-3 text-lg font-medium"
                      onClick={generateAllQRCodes}
                      disabled={isGeneratingQRs || (vcardData.length === 0 && !currentVCard.firstName && !currentVCard.lastName && !currentVCard.email)}
                    >
                      {isGeneratingQRs ? 'Generating QR Codes...' : `Generate ${vcardData.length > 0 ? `All ${vcardData.length}` : '1'} QR Code${vcardData.length > 1 ? 's' : ''} (1000x1000)`}
                    </Button>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-6">
              <div className="text-center space-y-4">
                <h3 className="text-2xl font-semibold">Generated QR Codes</h3>
                <p className="text-muted-foreground">
                  {generatedQRCodes.length > 0
                    ? `${generatedQRCodes.length} high-resolution (1000x1000) QR codes ready for download`
                    : "No QR codes generated yet. Please customize design and generate QR codes first."
                  }
                </p>

                {generatedQRCodes.length > 0 && (
                  <>
                    {/* Download Button */}
                    <div className="flex justify-center gap-4 mb-8">
                      <Button
                        onClick={downloadAllQRCodes}
                        size="lg"
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                        disabled={isGeneratingZip}
                      >
                        <Download className="h-5 w-5 mr-2" />
                        {isGeneratingZip ? 'Creating ZIP...' : `Download All ${generatedQRCodes.length} QR Codes (1000x1000)`}
                      </Button>
                    </div>

                    {/* QR Codes Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                      {generatedQRCodes.map((qrCode, index) => {
                        const contact = vcardData[index];
                        const contactName = `${contact?.firstName || 'Unknown'} ${contact?.lastName || 'Contact'}`.trim();

                        return (
                          <Card key={index} className="p-4">
                            <CardContent className="space-y-3">
                              <div className="text-center">
                                <img
                                  src={qrCode}
                                  alt={`QR Code for ${contactName}`}
                                  className="w-full max-w-[200px] h-auto mx-auto border rounded-lg"
                                />
                              </div>
                              <div className="text-center space-y-1">
                                <h4 className="font-medium text-sm">{contactName}</h4>
                                <p className="text-xs text-gray-600">{contact?.email || contact?.phone || 'No contact info'}</p>
                                <p className="text-xs text-gray-500">1000x1000 pixels</p>
                              </div>
                              <Button
                                size="sm"
                                variant="outline"
                                className="w-full"
                                onClick={() => downloadSingleQRCode(index)}
                              >
                                Download Individual
                              </Button>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </>
                )}
              </div>

              {generatedQRCodes.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Preview Grid - All {generatedQRCodes.length} QR Codes (800x800 resolution)</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                      {generatedQRCodes.map((qrCode, index) => {
                        const contact = vcardData[index];
                        return (
                          <div key={index} className="text-center space-y-2">
                            <div className="bg-muted rounded-lg p-2 aspect-square flex items-center justify-center">
                              <img 
                                src={qrCode} 
                                alt={`QR Code for ${contact.firstName} ${contact.lastName}`}
                                className="max-w-full max-h-full object-contain"
                              />
                            </div>
                            <p className="text-xs font-medium truncate" title={`${contact.firstName} ${contact.lastName}`}>
                              {contact.firstName} {contact.lastName}
                            </p>
                            {contact.organization && (
                              <p className="text-xs text-muted-foreground truncate" title={contact.organization}>
                                {contact.organization}
                              </p>
                            )}
                            <p className="text-xs text-green-600">800x800px</p>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Index;
