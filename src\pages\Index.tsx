import React, { useState, useRef, useEffect } from 'react';
import { Upload, Download, QrCode, Palette, Image as ImageIcon, UserCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import CSVImporter from '@/components/CSVImporter';
import QRCustomizer from '@/components/QRCustomizer';
import QRPreview from '@/components/QRPreview';
import DataReview from '@/components/DataReview';
import { useToast } from '@/hooks/use-toast';
import JSZip from 'jszip';
import QRCode from 'qrcode';

interface VCardData {
  firstName: string;
  lastName: string;
  organization: string;
  jobTitle: string;
  address: string;
  city: string;
  phone: string;
  email: string;
}

interface QRDesignSettings {
  foregroundColor: string;
  backgroundColor: string;
  style: 'square' | 'rounded' | 'dots';
  logo?: string;
  logoSize: number;
}

const Index = () => {
  const [vcardData, setVcardData] = useState<VCardData[]>([]);
  const [currentVCard, setCurrentVCard] = useState<VCardData>({
    firstName: '',
    lastName: '',
    organization: '',
    jobTitle: '',
    address: '',
    city: '',
    phone: '',
    email: '',
  });
  const [designSettings, setDesignSettings] = useState<QRDesignSettings>({
    foregroundColor: '#000000',
    backgroundColor: '#ffffff',
    style: 'square',
    logoSize: 20
  });
  const [activeTab, setActiveTab] = useState('import');
  const [isGeneratingZip, setIsGeneratingZip] = useState(false);
  const [generatedQRCodes, setGeneratedQRCodes] = useState<string[]>([]);
  const [isGeneratingQRs, setIsGeneratingQRs] = useState(false);
  const { toast } = useToast();

  const generateVCardString = (data: VCardData): string => {
    const fullAddress = [data.address, data.city].filter(Boolean).join(', ');
    return `BEGIN:VCARD
VERSION:3.0
FN:${data.firstName} ${data.lastName}
N:${data.lastName};${data.firstName};;;
ORG:${data.organization}
TITLE:${data.jobTitle}
TEL:${data.phone}
EMAIL:${data.email}
ADR:;;${fullAddress};;;;
END:VCARD`;
  };

  const handleCSVImport = (data: VCardData[]) => {
    setVcardData(data);
    if (data.length > 0) {
      setCurrentVCard(data[0]);
      setActiveTab('review');
      toast({
        title: "CSV Imported Successfully",
        description: `Imported ${data.length} contact(s) for review.`,
      });
    }
  };

  const handleManualEntry = () => {
    if (currentVCard.firstName || currentVCard.lastName || currentVCard.email) {
      const newData = [...vcardData, currentVCard];
      setVcardData(newData);
      setCurrentVCard({
        firstName: '',
        lastName: '',
        organization: '',
        jobTitle: '',
        address: '',
        city: '',
        phone: '',
        email: '',
      });
      setActiveTab('review');
      toast({
        title: "Contact Added",
        description: "Contact added for review.",
      });
    }
  };

  const handleDesignChange = (newSettings: Partial<QRDesignSettings>) => {
    setDesignSettings(prev => ({ ...prev, ...newSettings }));
  };

  const generateQRCodeDataURL = async (vCardString: string, settings: QRDesignSettings): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      QRCode.toCanvas(canvas, vCardString, {
        width: 800,
        margin: 4,
        color: {
          dark: settings.foregroundColor,
          light: settings.backgroundColor,
        },
        errorCorrectionLevel: 'M',
      }).then(() => {
        if (settings.logo) {
          const img = new Image();
          img.onload = () => {
            const logoSizePixels = (canvas.width * settings.logoSize) / 100;
            const x = (canvas.width - logoSizePixels) / 2;
            const y = (canvas.height - logoSizePixels) / 2;

            ctx.fillStyle = settings.backgroundColor;
            ctx.beginPath();
            ctx.arc(
              canvas.width / 2, 
              canvas.height / 2, 
              logoSizePixels / 2 + 8,
              0, 
              2 * Math.PI
            );
            ctx.fill();

            ctx.drawImage(img, x, y, logoSizePixels, logoSizePixels);
            resolve(canvas.toDataURL('image/png'));
          };
          img.onerror = () => resolve(canvas.toDataURL('image/png'));
          img.src = settings.logo;
        } else {
          resolve(canvas.toDataURL('image/png'));
        }
      }).catch(reject);
    });
  };

  const generateAllQRCodes = async () => {
    if (vcardData.length === 0) {
      toast({
        title: "No Data",
        description: "Please review and finalize contact data first.",
        variant: "destructive"
      });
      return;
    }

    setIsGeneratingQRs(true);
    toast({
      title: "Generating QR Codes",
      description: `Generating ${vcardData.length} QR codes...`,
    });

    try {
      const qrCodes: string[] = [];
      
      for (let i = 0; i < vcardData.length; i++) {
        const contact = vcardData[i];
        const vCardString = generateVCardString(contact);
        const dataURL = await generateQRCodeDataURL(vCardString, designSettings);
        qrCodes.push(dataURL);
      }

      setGeneratedQRCodes(qrCodes);
      setActiveTab('preview');
      
      toast({
        title: "QR Codes Generated",
        description: `Successfully generated ${qrCodes.length} high-resolution QR codes.`,
      });
    } catch (error) {
      console.error('Error generating QR codes:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate QR codes. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingQRs(false);
    }
  };

  const downloadAllQRCodes = async () => {
    if (generatedQRCodes.length === 0) {
      toast({
        title: "No QR Codes",
        description: "Please generate QR codes first.",
        variant: "destructive"
      });
      return;
    }

    setIsGeneratingZip(true);
    toast({
      title: "Preparing Download",
      description: `Preparing ${generatedQRCodes.length} high-resolution QR codes for download...`,
    });

    try {
      const zip = new JSZip();
      
      for (let i = 0; i < generatedQRCodes.length; i++) {
        const contact = vcardData[i];
        const dataURL = generatedQRCodes[i];
        
        const response = await fetch(dataURL);
        const blob = await response.blob();
        
        const fileName = `${contact.firstName || 'Unknown'}_${contact.lastName || 'Contact'}_QR_${i + 1}.png`
          .replace(/[^a-z0-9_]/gi, '_')
          .replace(/_+/g, '_')
          .replace(/^_|_$/g, '');
        
        zip.file(fileName, blob);
      }

      const zipBlob = await zip.generateAsync({ type: 'blob' });
      
      const url = URL.createObjectURL(zipBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `vcard_qr_codes_${vcardData.length}_contacts_800x800.zip`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Download Complete",
        description: `Successfully downloaded ${vcardData.length} high-resolution (800x800) QR codes.`,
      });
    } catch (error) {
      console.error('Error generating ZIP:', error);
      toast({
        title: "Download Failed",
        description: "Failed to create download. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingZip(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl">
              <QrCode className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              David's COP VCARD QR Generator
            </h1>
          </div>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Create professional QR codes for contact information with bulk CSV import and advanced customization options. Only for Cathedral of Praise.
          </p>
        </div>

        <div className="max-w-7xl mx-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-8">
              <TabsTrigger value="import" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                Import Data
              </TabsTrigger>
              <TabsTrigger value="review" className="flex items-center gap-2">
                <UserCheck className="h-4 w-4" />
                Review Data
              </TabsTrigger>
              <TabsTrigger value="customize" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Customize Design
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <QrCode className="h-4 w-4" />
                Preview & Download
              </TabsTrigger>
            </TabsList>

            <TabsContent value="import" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="border-2 border-dashed border-muted hover:border-primary/50 transition-colors">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Upload className="h-5 w-5" />
                      Bulk Import from CSV
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CSVImporter onDataImported={handleCSVImport} />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Manual Entry</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName">First Name</Label>
                        <Input
                          id="firstName"
                          value={currentVCard.firstName}
                          onChange={(e) => setCurrentVCard(prev => ({ ...prev, firstName: e.target.value }))}
                          placeholder="John"
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input
                          id="lastName"
                          value={currentVCard.lastName}
                          onChange={(e) => setCurrentVCard(prev => ({ ...prev, lastName: e.target.value }))}
                          placeholder="Doe"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="organization">Organization</Label>
                      <Input
                        id="organization"
                        value={currentVCard.organization}
                        onChange={(e) => setCurrentVCard(prev => ({ ...prev, organization: e.target.value }))}
                        placeholder="Company Name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="jobTitle">Job Title</Label>
                      <Input
                        id="jobTitle"
                        value={currentVCard.jobTitle}
                        onChange={(e) => setCurrentVCard(prev => ({ ...prev, jobTitle: e.target.value }))}
                        placeholder="Software Engineer"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="address">Address</Label>
                        <Input
                          id="address"
                          value={currentVCard.address}
                          onChange={(e) => setCurrentVCard(prev => ({ ...prev, address: e.target.value }))}
                          placeholder="123 Main St"
                        />
                      </div>
                      <div>
                        <Label htmlFor="city">City</Label>
                        <Input
                          id="city"
                          value={currentVCard.city}
                          onChange={(e) => setCurrentVCard(prev => ({ ...prev, city: e.target.value }))}
                          placeholder="New York"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="phone">Phone</Label>
                        <Input
                          id="phone"
                          value={currentVCard.phone}
                          onChange={(e) => setCurrentVCard(prev => ({ ...prev, phone: e.target.value }))}
                          placeholder="+****************"
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          value={currentVCard.email}
                          onChange={(e) => setCurrentVCard(prev => ({ ...prev, email: e.target.value }))}
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                    
                    <div className="flex justify-end">
                      <Button onClick={handleManualEntry}>
                        Add Contact for Review
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="review" className="space-y-6">
              <DataReview 
                data={vcardData}
                onDataChange={setVcardData}
                onProceedToCustomize={() => setActiveTab('customize')}
              />
            </TabsContent>

            <TabsContent value="customize" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <QRCustomizer 
                  settings={designSettings}
                  onSettingsChange={handleDesignChange}
                />
                <Card>
                  <CardHeader>
                    <CardTitle>Live Preview</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-center p-8">
                      <QRPreview 
                        vCardString={generateVCardString(vcardData[0] || currentVCard)}
                        settings={designSettings}
                      />
                    </div>
                    
                    <div className="flex justify-center">
                      <Button 
                        onClick={generateAllQRCodes}
                        disabled={isGeneratingQRs || vcardData.length === 0}
                        className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                      >
                        {isGeneratingQRs ? 'Generating...' : `Generate ${vcardData.length} QR Codes`}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-6">
              <div className="text-center space-y-4">
                <h3 className="text-2xl font-semibold">Generated QR Codes</h3>
                <p className="text-muted-foreground">
                  {generatedQRCodes.length > 0 
                    ? `${generatedQRCodes.length} high-resolution (800x800) QR codes ready for download`
                    : "No QR codes generated yet. Please customize design and generate QR codes first."
                  }
                </p>
                
                {generatedQRCodes.length > 0 && (
                  <div className="flex justify-center gap-4">
                    <Button 
                      onClick={downloadAllQRCodes} 
                      size="lg" 
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                      disabled={isGeneratingZip}
                    >
                      <Download className="h-5 w-5 mr-2" />
                      {isGeneratingZip ? 'Creating ZIP...' : `Download All ${generatedQRCodes.length} QR Codes (800x800)`}
                    </Button>
                  </div>
                )}
              </div>

              {generatedQRCodes.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Preview Grid - All {generatedQRCodes.length} QR Codes (800x800 resolution)</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                      {generatedQRCodes.map((qrCode, index) => {
                        const contact = vcardData[index];
                        return (
                          <div key={index} className="text-center space-y-2">
                            <div className="bg-muted rounded-lg p-2 aspect-square flex items-center justify-center">
                              <img 
                                src={qrCode} 
                                alt={`QR Code for ${contact.firstName} ${contact.lastName}`}
                                className="max-w-full max-h-full object-contain"
                              />
                            </div>
                            <p className="text-xs font-medium truncate" title={`${contact.firstName} ${contact.lastName}`}>
                              {contact.firstName} {contact.lastName}
                            </p>
                            {contact.organization && (
                              <p className="text-xs text-muted-foreground truncate" title={contact.organization}>
                                {contact.organization}
                              </p>
                            )}
                            <p className="text-xs text-green-600">800x800px</p>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Index;
