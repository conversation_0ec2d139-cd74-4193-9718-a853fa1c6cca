
import React, { useRef } from 'react';
import { Palette, Image as ImageIcon, Square, Circle, Hexagon } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface QRDesignSettings {
  foregroundColor: string;
  backgroundColor: string;
  style: 'square' | 'rounded' | 'dots';
  logo?: string;
  logoSize: number;
}

interface QRCustomizerProps {
  settings: QRDesignSettings;
  onSettingsChange: (settings: Partial<QRDesignSettings>) => void;
}

const QRCustomizer: React.FC<QRCustomizerProps> = ({ settings, onSettingsChange }) => {
  const logoInputRef = useRef<HTMLInputElement>(null);

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const logoDataUrl = event.target?.result as string;
        onSettingsChange({ logo: logoDataUrl });
      };
      reader.readAsDataURL(file);
    }
  };

  const presetColors = [
    '#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff',
    '#ffff00', '#ff00ff', '#00ffff', '#ffa500', '#800080',
    '#008000', '#000080', '#800000', '#808080', '#c0c0c0'
  ];

  const gradientColors = [
    'linear-gradient(45deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(45deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(45deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(45deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(45deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(45deg, #a8edea 0%, #fed6e3 100%)'
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="h-5 w-5" />
          Design Customization
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Colors Section */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <div className="w-4 h-4 bg-gradient-to-r from-red-500 to-blue-500 rounded"></div>
            Colors
          </h4>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Foreground Color</Label>
              <div className="flex gap-2">
                <Input
                  type="color"
                  value={settings.foregroundColor}
                  onChange={(e) => onSettingsChange({ foregroundColor: e.target.value })}
                  className="w-12 h-10 p-1 border rounded cursor-pointer"
                />
                <Input
                  type="text"
                  value={settings.foregroundColor}
                  onChange={(e) => onSettingsChange({ foregroundColor: e.target.value })}
                  className="flex-1"
                  placeholder="#000000"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Background Color</Label>
              <div className="flex gap-2">
                <Input
                  type="color"
                  value={settings.backgroundColor}
                  onChange={(e) => onSettingsChange({ backgroundColor: e.target.value })}
                  className="w-12 h-10 p-1 border rounded cursor-pointer"
                />
                <Input
                  type="text"
                  value={settings.backgroundColor}
                  onChange={(e) => onSettingsChange({ backgroundColor: e.target.value })}
                  className="flex-1"
                  placeholder="#ffffff"
                />
              </div>
            </div>
          </div>

          {/* Preset Colors */}
          <div className="space-y-2">
            <Label className="text-sm">Quick Colors</Label>
            <div className="grid grid-cols-8 gap-2">
              {presetColors.map((color, index) => (
                <button
                  key={index}
                  className="w-8 h-8 rounded border-2 border-muted hover:border-primary transition-colors"
                  style={{ backgroundColor: color }}
                  onClick={() => onSettingsChange({ foregroundColor: color })}
                  title={color}
                />
              ))}
            </div>
          </div>

          {/* Gradient Presets */}
          <div className="space-y-2">
            <Label className="text-sm">Gradient Inspiration</Label>
            <div className="grid grid-cols-3 gap-2">
              {gradientColors.map((gradient, index) => (
                <button
                  key={index}
                  className="h-8 rounded border-2 border-muted hover:border-primary transition-colors"
                  style={{ background: gradient }}
                  onClick={() => {
                    // Extract first color from gradient for simplicity
                    const match = gradient.match(/#[a-fA-F0-9]{6}/);
                    if (match) {
                      onSettingsChange({ foregroundColor: match[0] });
                    }
                  }}
                  title="Apply gradient colors"
                />
              ))}
            </div>
          </div>
        </div>

        {/* Style Section */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Square className="h-4 w-4" />
            QR Code Style
          </h4>
          
          <RadioGroup
            value={settings.style}
            onValueChange={(value: 'square' | 'rounded' | 'dots') => 
              onSettingsChange({ style: value })
            }
            className="grid grid-cols-3 gap-4"
          >
            <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
              <RadioGroupItem value="square" id="square" />
              <Label htmlFor="square" className="flex items-center gap-2 cursor-pointer">
                <Square className="h-4 w-4" />
                Square
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
              <RadioGroupItem value="rounded" id="rounded" />
              <Label htmlFor="rounded" className="flex items-center gap-2 cursor-pointer">
                <div className="w-4 h-4 bg-current rounded"></div>
                Rounded
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
              <RadioGroupItem value="dots" id="dots" />
              <Label htmlFor="dots" className="flex items-center gap-2 cursor-pointer">
                <Circle className="h-4 w-4" />
                Dots
              </Label>
            </div>
          </RadioGroup>
        </div>

        {/* Logo Section */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            Logo
          </h4>
          
          <div className="space-y-4">
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => logoInputRef.current?.click()}
                className="flex-1"
              >
                <ImageIcon className="h-4 w-4 mr-2" />
                {settings.logo ? 'Change Logo' : 'Upload Logo'}
              </Button>
              {settings.logo && (
                <Button
                  variant="outline"
                  onClick={() => onSettingsChange({ logo: undefined })}
                >
                  Remove
                </Button>
              )}
            </div>
            
            <input
              ref={logoInputRef}
              type="file"
              accept="image/*"
              onChange={handleLogoUpload}
              className="hidden"
            />

            {settings.logo && (
              <div className="space-y-4">
                <div className="flex justify-center">
                  <img
                    src={settings.logo}
                    alt="Logo preview"
                    className="max-w-16 max-h-16 object-contain border rounded"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Logo Size: {settings.logoSize}%</Label>
                  <Slider
                    value={[settings.logoSize]}
                    onValueChange={(value) => onSettingsChange({ logoSize: value[0] })}
                    max={40}
                    min={10}
                    step={1}
                    className="w-full"
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Tips */}
        <div className="bg-muted/50 rounded-lg p-4 space-y-2">
          <h5 className="font-medium text-sm">Design Tips</h5>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• Use high contrast colors for better scanability</li>
            <li>• Keep logo size between 10-30% for optimal reading</li>
            <li>• Test your QR codes before printing</li>
            <li>• Rounded styles work well for modern designs</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default QRCustomizer;
