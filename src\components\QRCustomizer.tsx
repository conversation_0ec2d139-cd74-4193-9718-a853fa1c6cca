
import React, { useRef, useState } from 'react';
import { Palette, Image as ImageIcon, Square, Circle, Hexagon, Eye, Grid3X3, Frame, Upload, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface QRDesignSettings {
  foregroundColor: string;
  backgroundColor: string;
  style: 'square' | 'rounded' | 'dots';
  logo?: string;
  logoSize: number;
  pattern?: string;
  eyeStyle?: string;
  frameStyle?: string;
  frameText?: string;
  frameFont?: string;
  template?: string;
  gradientType?: 'solid' | 'gradient';
}

interface QRCustomizerProps {
  settings: QRDesignSettings;
  onSettingsChange: (settings: Partial<QRDesignSettings>) => void;
}

const QRCustomizer: React.FC<QRCustomizerProps> = ({ settings, onSettingsChange }) => {
  const logoInputRef = useRef<HTMLInputElement>(null);
  const [activeTab, setActiveTab] = useState('pattern');

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const logoDataUrl = event.target?.result as string;
        onSettingsChange({ logo: logoDataUrl });
      };
      reader.readAsDataURL(file);
    }
  };

  // Template data
  const templates = [
    { id: 'none', name: 'None', color: 'transparent', preview: '×' },
    { id: 'blue-social', name: 'Blue Social', color: '#1e40af', preview: 'SCAN ME' },
    { id: 'black-follow', name: 'Black Follow', color: '#000000', preview: 'FOLLOW US' },
    { id: 'orange-scan', name: 'Orange Scan', color: '#ea580c', preview: 'SCAN ME' },
    { id: 'purple-follow', name: 'Purple Follow', color: '#7c3aed', preview: 'FOLLOW US' },
    { id: 'red-follow', name: 'Red Follow', color: '#dc2626', preview: 'FOLLOW US' },
    { id: 'blue-profile', name: 'Blue Profile', color: '#2563eb', preview: 'PROFILE' },
    { id: 'green-profile', name: 'Green Profile', color: '#16a34a', preview: 'PROFILE' },
    { id: 'red-pdf', name: 'Red PDF', color: '#dc2626', preview: 'PDF FILE' },
    { id: 'blue-contact', name: 'Blue Contact', color: '#2563eb', preview: 'CONTACT' },
    { id: 'teal-scan', name: 'Teal Scan', color: '#0d9488', preview: 'SCAN ME' },
    { id: 'brown-scan', name: 'Brown Scan', color: '#a16207', preview: 'SCAN ME' },
    { id: 'navy-scan', name: 'Navy Scan', color: '#1e3a8a', preview: 'SCAN ME' },
    { id: 'brown-scan2', name: 'Brown Scan 2', color: '#92400e', preview: 'SCAN ME' },
    { id: 'blue-scan2', name: 'Blue Scan 2', color: '#1d4ed8', preview: 'SCAN ME' },
    { id: 'blue-scan3', name: 'Blue Scan 3', color: '#2563eb', preview: 'SCAN ME' },
    { id: 'red-scan', name: 'Red Scan', color: '#dc2626', preview: 'SCAN ME' },
    { id: 'teal-scan2', name: 'Teal Scan 2', color: '#0f766e', preview: 'SCAN ME' },
    { id: 'black-playlist', name: 'Black Playlist', color: '#000000', preview: 'PLAYLIST' },
    { id: 'teal-scan3', name: 'Teal Scan 3', color: '#0d9488', preview: 'SCAN ME' }
  ];

  // Frame styles
  const frameStyles = [
    { id: 'none', name: 'None', preview: '×' },
    { id: 'square', name: 'Square', preview: '⬜' },
    { id: 'rounded', name: 'Rounded', preview: '⬜' },
    { id: 'circle', name: 'Circle', preview: '⭕' },
    { id: 'oval', name: 'Oval', preview: '⭕' }
  ];

  // Eye styles
  const eyeStyles = [
    { id: 'square', name: 'Square', preview: '⬛' },
    { id: 'circle', name: 'Circle', preview: '⚫' },
    { id: 'rounded', name: 'Rounded', preview: '⬜' },
    { id: 'leaf', name: 'Leaf', preview: '🍃' },
    { id: 'star', name: 'Star', preview: '⭐' }
  ];

  // Pattern styles
  const patternStyles = [
    { id: 'square', name: 'Square', preview: '⬛' },
    { id: 'dots', name: 'Dots', preview: '⚫' },
    { id: 'rounded', name: 'Rounded', preview: '⬜' },
    { id: 'diamond', name: 'Diamond', preview: '♦️' },
    { id: 'heart', name: 'Heart', preview: '❤️' },
    { id: 'star', name: 'Star', preview: '⭐' }
  ];

  // Social media logos
  const socialLogos = [
    { id: 'facebook', name: 'Facebook', color: '#1877f2' },
    { id: 'instagram', name: 'Instagram', color: '#e4405f' },
    { id: 'pinterest', name: 'Pinterest', color: '#bd081c' },
    { id: 'twitter', name: 'Twitter/X', color: '#000000' },
    { id: 'youtube', name: 'YouTube', color: '#ff0000' },
    { id: 'snapchat', name: 'Snapchat', color: '#fffc00' },
    { id: 'tiktok', name: 'TikTok', color: '#000000' },
    { id: 'linkedin', name: 'LinkedIn', color: '#0077b5' },
    { id: 'whatsapp', name: 'WhatsApp', color: '#25d366' },
    { id: 'telegram', name: 'Telegram', color: '#0088cc' },
    { id: 'weme', name: 'WeMe', color: '#ff6b6b' },
    { id: 'teams', name: 'Teams', color: '#6264a7' },
    { id: 'maps', name: 'Maps', color: '#4285f4' },
    { id: 'playstore', name: 'Play Store', color: '#01875f' },
    { id: 'gmail', name: 'Gmail', color: '#ea4335' },
    { id: 'paypal', name: 'PayPal', color: '#003087' },
    { id: 'vimeo', name: 'Vimeo', color: '#1ab7ea' },
    { id: 'spotify', name: 'Spotify', color: '#1db954' },
    { id: 'wechat', name: 'WeChat', color: '#7bb32e' },
    { id: 'yelp', name: 'Yelp', color: '#d32323' }
  ];

  // Pattern designs that match the screenshot
  const patternDesigns = [
    { id: 'pattern1', pattern: '■■■■■\n■   ■\n■ ■ ■\n■   ■\n■■■■■' },
    { id: 'pattern2', pattern: '● ● ●\n ● ● \n● ● ●\n ● ● \n● ● ●' },
    { id: 'pattern3', pattern: '· · ·\n · · \n· · ·\n · · \n· · ·' },
    { id: 'pattern4', pattern: '▓▓▓▓▓\n▓   ▓\n▓ ▓ ▓\n▓   ▓\n▓▓▓▓▓' },
    { id: 'pattern5', pattern: '████\n█  █\n█  █\n████' },
    { id: 'pattern6', pattern: '▲▲▲▲\n▲  ▲\n▲  ▲\n▲▲▲▲' },
    { id: 'pattern7', pattern: '♦♦♦♦\n♦  ♦\n♦  ♦\n♦♦♦♦' },
    { id: 'pattern8', pattern: '▼▼▼▼\n▼  ▼\n▼  ▼\n▼▼▼▼' },
    { id: 'pattern9', pattern: '◆◆◆◆\n◆  ◆\n◆  ◆\n◆◆◆◆' },
    { id: 'pattern10', pattern: '★★★★\n★  ★\n★  ★\n★★★★' },
    { id: 'pattern11', pattern: '●●●●\n●  ●\n●  ●\n●●●●' },
    { id: 'pattern12', pattern: '◊◊◊◊\n◊  ◊\n◊  ◊\n◊◊◊◊' }
  ];

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="flex bg-gray-100 rounded-lg p-1">
        <button
          type="button"
          className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'pattern' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'
          }`}
          onClick={() => setActiveTab('pattern')}
        >
          <Grid3X3 className="h-4 w-4" />
          Pattern
        </button>
        <button
          type="button"
          className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'eyes' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'
          }`}
          onClick={() => setActiveTab('eyes')}
        >
          <Eye className="h-4 w-4" />
          Eyes
        </button>
        <button
          type="button"
          className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'logo' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'
          }`}
          onClick={() => setActiveTab('logo')}
        >
          <ImageIcon className="h-4 w-4" />
          Logo
        </button>
        <button
          type="button"
          className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'colors' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'
          }`}
          onClick={() => setActiveTab('colors')}
        >
          <Palette className="h-4 w-4" />
          Colors
        </button>
        <button
          type="button"
          className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'frame' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'
          }`}
          onClick={() => setActiveTab('frame')}
        >
          <Frame className="h-4 w-4" />
          Frame
        </button>
        <button
          type="button"
          className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'templates' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'
          }`}
          onClick={() => setActiveTab('templates')}
        >
          <Square className="h-4 w-4" />
          Templates
        </button>
      </div>

      {/* Content Area */}
      {activeTab === 'pattern' && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm text-blue-600">
            <div className="w-4 h-4 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-xs">ℹ</span>
            </div>
            You can customize these templates later to match your brand
          </div>

          <div className="grid grid-cols-6 gap-3">
            {patternDesigns.map((pattern, index) => (
              <button
                key={pattern.id}
                type="button"
                className={`aspect-square border-2 rounded-lg p-2 hover:border-blue-500 transition-colors ${
                  settings.pattern === pattern.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
                onClick={() => onSettingsChange({ pattern: pattern.id })}
                title={`Pattern ${index + 1}`}
              >
                <div className="w-full h-full flex items-center justify-center">
                  <div className="grid grid-cols-5 gap-px">
                    {/* Generate pattern based on index */}
                    {Array.from({ length: 25 }, (_, i) => {
                      const row = Math.floor(i / 5);
                      const col = i % 5;
                      let shouldFill = false;

                      // Different patterns based on index
                      switch (index) {
                        case 0: // Square pattern
                          shouldFill = (row === 0 || row === 4 || col === 0 || col === 4) || (row === 2 && col === 2);
                          break;
                        case 1: // Dots pattern
                          shouldFill = (row + col) % 2 === 0;
                          break;
                        case 2: // Sparse dots
                          shouldFill = (row % 2 === 0 && col % 2 === 0);
                          break;
                        case 3: // Dense pattern
                          shouldFill = Math.random() > 0.3;
                          break;
                        case 4: // Vertical lines
                          shouldFill = col % 2 === 0;
                          break;
                        case 5: // Diagonal
                          shouldFill = row === col || row + col === 4;
                          break;
                        case 6: // Cross pattern
                          shouldFill = row === 2 || col === 2;
                          break;
                        case 7: // Corner pattern
                          shouldFill = (row < 2 && col < 2) || (row > 2 && col > 2);
                          break;
                        case 8: // Checkerboard
                          shouldFill = (row + col) % 2 === 1;
                          break;
                        case 9: // Border with center
                          shouldFill = (row === 0 || row === 4 || col === 0 || col === 4) && (row === 2 || col === 2);
                          break;
                        case 10: // Random dense
                          shouldFill = Math.random() > 0.4;
                          break;
                        case 11: // Diamond pattern
                          shouldFill = Math.abs(row - 2) + Math.abs(col - 2) <= 2;
                          break;
                        default:
                          shouldFill = Math.random() > 0.5;
                      }

                      return (
                        <div
                          key={i}
                          className={`w-1 h-1 ${shouldFill ? 'bg-black' : 'bg-transparent'}`}
                        />
                      );
                    })}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'eyes' && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm text-blue-600">
            <div className="w-4 h-4 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-xs">ℹ</span>
            </div>
            Select eyes to make your QR code stand out. Eyes are what your camera recognizes when scanning
          </div>
          <div className="grid grid-cols-6 gap-3">
            {eyeStyles.map((eye) => (
              <button
                key={eye.id}
                type="button"
                className={`aspect-square border-2 rounded-lg p-2 hover:border-blue-500 transition-colors ${
                  settings.eyeStyle === eye.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
                onClick={() => onSettingsChange({ eyeStyle: eye.id })}
                title={eye.name}
              >
                <div className="w-full h-full border-4 border-black rounded flex items-center justify-center">
                  <div className="w-1/2 h-1/2 bg-black rounded"></div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'logo' && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm text-blue-600">
            <div className="w-4 h-4 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-xs">ℹ</span>
            </div>
            With this QR code generator with logo, you can easily add your logo for stronger brand recall (300 x 300px, 72dpi)
          </div>

          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg mx-auto flex items-center justify-center">
                {settings.logo ? (
                  <img src={settings.logo} alt="Logo" className="w-full h-full object-contain" />
                ) : (
                  <ImageIcon className="w-8 h-8 text-gray-400" />
                )}
              </div>

              <div className="space-y-2">
                <Button
                  onClick={() => logoInputRef.current?.click()}
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  Upload
                </Button>
                {settings.logo && (
                  <Button
                    variant="outline"
                    onClick={() => onSettingsChange({ logo: undefined })}
                  >
                    Remove logo
                  </Button>
                )}
              </div>

              <div className="text-xs text-gray-500">
                <div>Supported formats:</div>
                <div className="flex gap-2 justify-center mt-1">
                  <span className="bg-gray-100 px-2 py-1 rounded">PNG</span>
                  <span className="bg-gray-100 px-2 py-1 rounded">JPG</span>
                </div>
              </div>
            </div>
          </div>

          <input
            ref={logoInputRef}
            type="file"
            accept="image/*"
            onChange={handleLogoUpload}
            className="hidden"
            aria-label="Upload logo file"
          />

          {/* Available Logos */}
          <div className="space-y-3">
            <div className="text-sm font-medium">Or use our available logos:</div>
            <div className="grid grid-cols-8 gap-3">
              {socialLogos.map((logo) => (
                <button
                  key={logo.id}
                  type="button"
                  className="aspect-square rounded-full border-2 border-gray-200 hover:border-blue-500 transition-colors flex items-center justify-center text-white text-xs font-bold"
                  style={{ backgroundColor: logo.color }}
                  onClick={() => onSettingsChange({ logo: `/logos/${logo.id}.png` })}
                  title={logo.name}
                >
                  {logo.name.charAt(0)}
                </button>
              ))}
            </div>
          </div>

          {settings.logo && (
            <div className="space-y-2">
              <Label>Logo Size: {settings.logoSize}%</Label>
              <Slider
                value={[settings.logoSize]}
                onValueChange={(value) => onSettingsChange({ logoSize: value[0] })}
                max={40}
                min={10}
                step={1}
                className="w-full"
              />
            </div>
          )}
        </div>
      )}

      {/* Add other tabs content here if needed */}
      {activeTab === 'colors' && (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Foreground Color</Label>
              <div className="flex gap-2">
                <input
                  type="color"
                  value={settings.foregroundColor}
                  onChange={(e) => onSettingsChange({ foregroundColor: e.target.value })}
                  className="w-12 h-10 p-1 border rounded cursor-pointer"
                  title="Foreground color picker"
                />
                <Input
                  type="text"
                  value={settings.foregroundColor}
                  onChange={(e) => onSettingsChange({ foregroundColor: e.target.value })}
                  className="flex-1"
                  placeholder="#000000"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Background Color</Label>
              <div className="flex gap-2">
                <input
                  type="color"
                  value={settings.backgroundColor}
                  onChange={(e) => onSettingsChange({ backgroundColor: e.target.value })}
                  className="w-12 h-10 p-1 border rounded cursor-pointer"
                  title="Background color picker"
                />
                <Input
                  type="text"
                  value={settings.backgroundColor}
                  onChange={(e) => onSettingsChange({ backgroundColor: e.target.value })}
                  className="flex-1"
                  placeholder="#ffffff"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'frame' && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm text-blue-600">
            <div className="w-4 h-4 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-xs">ℹ</span>
            </div>
            A quick response code with a frame and call-to-action gets 80% more scans
          </div>

          <div className="grid grid-cols-6 gap-3">
            {frameStyles.map((frame) => (
              <button
                key={frame.id}
                type="button"
                className={`aspect-square border-2 rounded-lg p-2 hover:border-blue-500 transition-colors ${
                  settings.frameStyle === frame.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
                onClick={() => onSettingsChange({ frameStyle: frame.id })}
                title={frame.name}
              >
                <div className="w-full h-full bg-black rounded flex items-center justify-center text-white text-xs">
                  {frame.preview}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'templates' && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm text-blue-600">
            <div className="w-4 h-4 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-xs">ℹ</span>
            </div>
            Choose a ready-made template (optional)
          </div>

          <div className="grid grid-cols-6 gap-3">
            {templates.map((template) => (
              <button
                key={template.id}
                type="button"
                className={`aspect-square border-2 rounded-lg p-1 hover:border-blue-500 transition-colors ${
                  settings.template === template.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
                onClick={() => onSettingsChange({ template: template.id })}
                title={template.name}
              >
                <div
                  className="w-full h-full rounded flex items-center justify-center text-white text-xs font-bold"
                  style={{ backgroundColor: template.color }}
                >
                  {template.preview}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default QRCustomizer;
